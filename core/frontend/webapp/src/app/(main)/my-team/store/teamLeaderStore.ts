import { create } from "zustand";
import api from "@/lib/axios";
import { toast } from "@/hooks/use-toast";
import {
  signalRService,
  type OperatorStatusUpdate,
  type ShiftInstantStatusUpdate,
  type TeamStatusMonitoringUpdate,
} from "@/services/signalRService";
import { useAttendanceStore } from "../store/myTeamStore";

// Types
export type AttendanceStatus =
  | "P"
  | "CTN"
  | "MA"
  | "AB"
  | "REPLACE"
  | "P_IN_BUS"
  | "P_IN_PLANT"
  | "W"
  | "Y"
  | "X"
  | "";

export interface TeamOperator {
  id: number;
  mle: string;
  firstName: string;
  lastName: string;
  role: string;
  function: string;
  team: string;
  line?: string;
  status?: AttendanceStatus;
}

export interface OperatorStatusData {
  id: string;
  legacy_id: number;
  first_name: string;
  last_name: string;
  function: string;
  shift_type: string;
  start?: string;
  end?: string;
  dailyRecords: DailyRecord[];
}

export interface TeamLeaderStatusData {
  id: string;
  legacy_id: number;
  first_name: string;
  last_name: string;
  teamleader_instant_id: string;
  teamleader_instant_status:
    | "INIT"
    | "VISUAL_CHECK"
    | "STARTED"
    | "CLOSED"
    | "REVIEW"
    | "VALIDATED"
    | string;
  shift_type: string;
  start?: string;
  end?: string;
  dailyRecords: DailyRecord[];
}

export interface ShiftLeaderStatusResponse {
  last_date: string;
  shift_instant_id: string;
  shift_start: string;
  shift_end: string;
  operators_status_data: OperatorStatusData[];
  teamleaders_status_data: TeamLeaderStatusData[];
  operators_shift_types: string[];
  teamleaders_shift_types: string[];
  teams_under_review: Array<{
    team_id: string;
    team_name: string;
    comment: string;
    review_date: string;
  }>;
}

export interface TeamOperatorsStatusRecord {
  id: string;
  legacy_id: number;
  first_name: string;
  last_name: string;
  role: string;
  team_id: string;
  dailyRecords: DailyRecord[];
  isMFG?: boolean;
  shift_type?: string;
}

export interface TeamOperatorsStatusResponse {
  last_date: string;
  shift_instant_id: string;
  shift_type: string;
  shift_status: string;
  shift_start: string;
  shift_end: string;
  status_data: TeamOperatorsStatusRecord[];
}

export interface TeamApiResponse {
  teamId: string;
  teamName: string;
  operators: TeamOperator[];
  shiftInstantId: string;
  date: string;
}

export interface StatusCode {
  code: string;
  startDateTime: string;
}

export interface OperatorStatusDetail {
  code: string;
  description: string;
  activatedAt: string;
  endedAt?: string;
}

export interface OperatorStatusTimeSummary {
  status_code: string;
  status_description: string;
  total_time_minutes: number;
  total_time_formatted: string;
}

export interface OperatorDetailsResponse {
  id: string;
  current_status: OperatorStatusDetail;
  status_history: OperatorStatusDetail[];
  scheduled_status: OperatorStatusDetail[];
  status_time_summary: OperatorStatusTimeSummary[];
}

export interface DailyRecord {
  date: string;
  statusCodes: StatusCode[];
}

export interface StatusMonitoringOperator {
  id: string;
  legacy_id: number;
  uuid: string;
  first_name: string;
  last_name: string;
  role: string;
  team_id: string;
  dailyRecords: DailyRecord[];
}

export interface StatusMonitoringResponse {
  last_date: string;
  shift_instant_id: string;
  shift_status: string;
  shift_start: string;
  shift_end: string;
  teams_under_review: Array<{
    team_id: string;
    team_name: string;
    comment: string;
    review_date: string;
  }>;
  status_data: StatusMonitoringOperator[];
}

export interface TeamLeaderInstantStatusItem {
  id: string;
  legacy_id: number;
  first_name: string;
  last_name: string;
  teamleader_instant_id: string;
  teamleader_instant_status:
    | "INIT"
    | "VISUAL_CHECK"
    | "STARTED"
    | "CLOSED"
    | "REVIEW"
    | "VALIDATED"
    | string;
  shift_type: string;
  start?: string;
  end?: string;
  dailyRecords: DailyRecord[];
}

export interface CurrentStatusResponse {
  shiftInstantId: string;
  teamId: string;
  operators: {
    id: number;
    uuid :string,
    mle: string;
    firstName: string;
    lastName: string;
    role: string;
    function: string;
    team: string;
    line?: string;
    status: AttendanceStatus;
  }[];
}

export interface UpdateStatusResponse {
  success: boolean;
  message: string;
}

export interface StatusInfo {
  id: string;
  statusCode: string;
  statusLabel: string;
  statusName: string;
  type: "INSTANT" | "SCHEDULE";
  color: string;
  createdAt: string;
  updatedAt: string;
}

export interface StatusHistoryEntry {
  date: string;
  status: AttendanceStatus;
  timestamp: string;
  submittedBy: string;
}

export interface StatusHistoryResponse {
  operatorId: number;
  history: StatusHistoryEntry[];
}

// DEPARTMENT_CLERK types
export interface ClerkShiftType {
  clerk_instant_id: string;
  shift_type: string;
}

export interface ClerkShiftTypesResponse {
  count: number;
  shift_types: ClerkShiftType[];
  date: string;
  clerk_id: string;
}

export interface ClerkSubordinateStatusData {
  id: string;
  legacy_id: number;
  first_name: string;
  last_name: string;
  role: string;
  team_id: string;
  shift_type: string;
  category: string;
  function : string;
  dailyRecords: DailyRecord[];
}

export interface ClerkStatusMonitoringResponse {
  last_date: string;
  clerk_shift_instant_id: string;
  shift_status_IS: string;
  shift_status_IS_comment: string;
  shift_status_IH: string;
  shift_status_IH_comment: string;
  status_data: ClerkSubordinateStatusData[];
}

// QUALITY_SUPERVISOR types
export interface QualityShiftType {
  designation: string;
  shift_instant_id: string;
  shift_type: string;
}

export interface QualityShiftTypesResponse {
  count: number;
  shift_types: QualityShiftType[];
  date: string;
  superior_id: string;
}

export interface QualitySubordinateStatusData {
  id: string;
  legacy_id: number;
  first_name: string;
  last_name: string;
  role: string;
  function: string;
  team_id: string;
  isMFG: boolean;
  dailyRecords: DailyRecord[];
}

export interface QualityStatusMonitoringResponse {
  last_date: string;
  shift_instant_id: string;
  shift_leader_instant_id: string;
  shift_status: string;
  shift_type: string;
  shift_start: string;
  shift_end: string;
  teams_under_review: Array<{
    team_id: string;
    team_name: string;
    comment: string;
    review_date: string;
  }>;
  status_data: QualitySubordinateStatusData[];
}

// Helper to extract backend error message
interface ErrorDetails {
  error?: { message?: string };
}
interface ErrorResponseData {
  message?: string;
  details?: ErrorDetails;
}
interface AxiosLikeError {
  response?: { data?: ErrorResponseData };
  message?: string;
}

function getApiErrorMessage(error: unknown): string | undefined {
  if (!error) return undefined;
  if (typeof error === "object" && error !== null && "response" in error) {
    const errObj = error as AxiosLikeError;
    const data = errObj.response?.data;
    if (data) {
      if (data.details?.error?.message) return data.details.error.message;
      if (data.message) return data.message;
    }
    if (errObj.message) return errObj.message;
  }
  if (typeof error === "string") return error;
  return undefined;
}

interface TeamLeaderState {
  // Data
  teamData: TeamApiResponse | null;
  currentStatusData: CurrentStatusResponse | null;
  statusMonitoringData: StatusMonitoringResponse | null;
  teamLeadersStatusData: TeamLeaderInstantStatusItem[] | null;
  teamOperatorsStatusData: TeamOperatorsStatusResponse | null;
  statusHistory: { [operatorId: number]: StatusHistoryEntry[] };
  statusInfoList: StatusInfo[];
  operatorDetails: { [operatorId: string]: OperatorDetailsResponse };
  statusMonitoringListenersSetup: boolean;

  operatorsStatusData: OperatorStatusData[] | null;
  teamLeadersRawStatusData: TeamLeaderStatusData[] | null;
  operatorsShiftTypes: string[];
  teamLeadersShiftTypes: string[];
  shiftLeaderStatusResponse: ShiftLeaderStatusResponse | null;
  // Loading states
  isTeamDataLoading: boolean;
  isCurrentStatusLoading: boolean;
  isStatusMonitoringLoading: boolean;
  isStatusHistoryLoading: boolean;
  isUpdatingStatus: boolean;
  isStatusInfoLoading: boolean;
  isOperatorDetailsLoading: boolean;

  // Error states
  teamDataError: string | null;
  currentStatusError: string | null;
  statusMonitoringError: string | null;
  statusHistoryError: string | null;
  updateStatusError: string | null;
  statusInfoError: string | null;
  operatorDetailsError: string | null;

  shiftTypes: Array<{ shiftInstantId: string; shiftType: string ; designation : string}>;
  isShiftTypesLoading: boolean;
  shiftTypesError: string | null;

  // DEPARTMENT_CLERK state
  clerkShiftTypes: ClerkShiftType[];
  isClerkShiftTypesLoading: boolean;
  clerkShiftTypesError: string | null;
  clerkStatusMonitoringData: ClerkStatusMonitoringResponse | null;
  isClerkStatusMonitoringLoading: boolean;
  clerkStatusMonitoringError: string | null;

  // DEPARTMENT_MANAGER state
  managerShiftTypes: ClerkShiftType[];
  isManagerShiftTypesLoading: boolean;
  managerShiftTypesError: string | null;
  managerStatusMonitoringData: ClerkStatusMonitoringResponse | null;
  isManagerStatusMonitoringLoading: boolean;
  managerStatusMonitoringError: string | null;

  // QUALITY_SUPERVISOR state
  qualityShiftTypes: QualityShiftType[];
  isQualityShiftTypesLoading: boolean;
  qualityShiftTypesError: string | null;
  qualityStatusMonitoringData: QualityStatusMonitoringResponse | null;
  isQualityStatusMonitoringLoading: boolean;
  qualityStatusMonitoringError: string | null;

  // Actions
  fetchTeamData: (teamId: string, startDate: string) => Promise<void>;
  fetchCurrentStatus: (teamId: string, date: string) => Promise<void>;
  fetchStatusMonitoring: (
    endDate: string,
    shiftInstantId?: string,
    mfg?: boolean
  ) => Promise<void>;
  fetchShiftLeaderStatusMonitoring: (endDate: string) => Promise<void>;
  fetchTeamOperatorsStatus: (
    teamLeaderInstantId: string,
    teamId: string
  ) => Promise<void>;
  changeTeamStatus: (payload: {
    shiftLeaderInstantId: string;
    teamLeaderInstantId: string;
    teamId: string;
    status: "REVIEW" | "VALIDATED";
  }) => Promise<void>;
  fetchStatusHistory: (teamId: string, operatorId: number) => Promise<void>;
  updateOperatorStatus: (
    shiftInstantId: string,
    operatorId: string,
    status: AttendanceStatus
  ) => Promise<void>;
  updateShiftStatus: (shiftInstantId: string, status: string) => Promise<void>;
  updateOperatorAttendanceStatus: (
    shiftInstantId: string,
    operatorId: string,
    statusCode: "P" | "AB"
  ) => Promise<void>;
  updateOperatorAttendanceStatusBulk: (
    shiftInstantId: string,
    statusCode: string,
    operatorIds: string[]
  ) => Promise<void>;
  updateClerkSubordinateStatus: (
    clerkInstantId: string,
    subordinateId: string,
    statusCode: string
  ) => Promise<void>;
  updateClerkSubordinateStatusBulk: (
    clerkInstantId: string,
    statusCode: string,
    subordinateIds: string[]
  ) => Promise<void>;
  updateManagerStatusMonitoringDataLocally: (clerkInstantId: string, employeeType: string, status: string) => void;
  fetchStatusInfo: () => Promise<void>;
  fetchOperatorDetails: (
    operatorId: string,
    shiftInstantId: string
  ) => Promise<void>;
  fetchShiftTypes: (
    date: Date
  ) => Promise<
    Array<{
      shiftInstantId: string /* add other shift type fields as needed */;
    }>
  >;

    setTeamLeaderShiftType: (shiftType: string) => void;

    setOperatorShiftType: (shiftType: string) => void;

  // DEPARTMENT_CLERK actions
  fetchClerkShiftTypes: (date: Date) => Promise<ClerkShiftType[]>;
  fetchClerkStatusMonitoring: (date: Date, clerkInstantId?: string) => Promise<void>;
  approveClerkShift: (clerkInstantId: string, employeeType: string) => Promise<void>;

  // DEPARTMENT_MANAGER actions
  fetchManagerShiftTypes: (date: Date) => Promise<ClerkShiftType[]>;
  fetchManagerStatusMonitoring: (date: Date, clerkInstantId?: string) => Promise<void>;
  fetchManagerSubordinateDetails: (clerkInstantId: string, subordinateId: string) => Promise<any>;
  approveManagerShift: (clerkInstantId: string, employeeType: string) => Promise<void>;

  sendForReviewManagerShift : (clerkInstantId: string, employeeType: string , comment : string) => Promise<void>;

  // QUALITY_SUPERVISOR actions
  fetchQualityShiftTypes: (date: Date) => Promise<QualityShiftType[]>;
  fetchQualityStatusMonitoring: (date: Date, qualityInstantId?: string) => Promise<void>;
  updateQualitySubordinateStatus: (
    qualityInstantId: string,
    subordinateId: string,
    statusCode: string
  ) => Promise<void>;
  updateQualitySubordinateStatusBulk: (
    qualityInstantId: string,
    statusCode: string,
    subordinateIds: string[]
  ) => Promise<void>;
  approveQualityShift: (qualityInstantId: string, shiftStatus?: string) => Promise<void>;

  // SignalR methods
  connectToStatusMonitoring: () => Promise<void>;
  disconnectFromStatusMonitoring: () => Promise<void>;
  setupStatusMonitoringListeners: () => void;
  joinStatusMonitoringGroups: (
    teamId?: string,
    shiftInstantId?: string
  ) => Promise<void>;
  leaveStatusMonitoringGroups: (
    teamId?: string,
    shiftInstantId?: string
  ) => Promise<void>;

  // Getters
  getTeamOperators: () => TeamOperator[];
  getCurrentStatusForOperator: (operatorId: number) => AttendanceStatus;
  getStatusHistoryForOperator: (operatorId: number) => StatusHistoryEntry[];
  getStatusMonitoringOperators: () => StatusMonitoringOperator[];

  // Local update methods
  updateStatusMonitoringDataLocally: (operatorId: string, statusCode: string) => void;
  updateTeamLeadersStatusDataLocally: (teamLeaderId: number, statusCode: string) => void;

  updateClerkStatusMonitoringDataLocally : (clerkInstantId: string, employeeType: string, status: string) => void;
  // Clear actions
  clearTeamData: () => void;
  clearCurrentStatus: () => void;
  clearStatusMonitoring: () => void;
  clearStatusHistory: () => void;
  clearErrors: () => void;
  clearShiftTypes: () => void;
  teamLeaderShiftType: string;
  operatorShiftType: string;
}

export const useTeamLeaderStore = create<TeamLeaderState>((set, get) => ({


    setTeamLeaderShiftType: (shiftType: string) => {
      set({ teamLeaderShiftType: shiftType });
    },

    setOperatorShiftType: (shiftType: string) => {
      set({ operatorShiftType: shiftType });
    },
  
    operatorShiftType: "NORMAL",
  teamLeaderShiftType: "NORMAL",
  operatorsStatusData: null,
  teamLeadersRawStatusData: null,
  operatorsShiftTypes: [],
  teamLeadersShiftTypes: [],
  shiftLeaderStatusResponse: null,

  teamData: null,
  currentStatusData: null,
  statusMonitoringData: null,
  teamLeadersStatusData: null,
  teamOperatorsStatusData: null,
  statusHistory: {},
  statusInfoList: [],
  operatorDetails: {},
  statusMonitoringListenersSetup: false,

  isTeamDataLoading: false,
  isCurrentStatusLoading: false,
  isStatusMonitoringLoading: false,
  isStatusHistoryLoading: false,
  isUpdatingStatus: false,
  isStatusInfoLoading: false,
  isOperatorDetailsLoading: false,

  teamDataError: null,
  currentStatusError: null,
  statusMonitoringError: null,
  statusHistoryError: null,
  updateStatusError: null,
  statusInfoError: null,
  operatorDetailsError: null,

  shiftTypes: [],
  isShiftTypesLoading: false,
  shiftTypesError: null,

  // DEPARTMENT_CLERK initial state
  clerkShiftTypes: [],
  isClerkShiftTypesLoading: false,
  clerkShiftTypesError: null,
  clerkStatusMonitoringData: null,
  isClerkStatusMonitoringLoading: false,
  clerkStatusMonitoringError: null,

  // DEPARTMENT_MANAGER initial state
  managerShiftTypes: [],
  isManagerShiftTypesLoading: false,
  managerShiftTypesError: null,
  managerStatusMonitoringData: null,
  isManagerStatusMonitoringLoading: false,
  managerStatusMonitoringError: null,

  // QUALITY_SUPERVISOR initial state
  qualityShiftTypes: [],
  isQualityShiftTypesLoading: false,
  qualityShiftTypesError: null,
  qualityStatusMonitoringData: null,
  isQualityStatusMonitoringLoading: false,
  qualityStatusMonitoringError: null,

  // Actions
  fetchTeamData: async (teamId: string, startDate: string) => {
    set({ isTeamDataLoading: true, teamDataError: null });

    try {
      const response = await api.get<TeamApiResponse>(
        `/status-tracking/api/teams/${teamId}/shift_instant/history`,
        {
          params: { startDate },
        }
      );

      set({
        teamData: response.data,
        isTeamDataLoading: false,
      });
    } catch (error) {
      const errorMessage =
        error && typeof error === "object" && "message" in error
          ? (error as Error).message
          : String(error);
      console.log(
        `${new Date().toLocaleTimeString()} - Error fetching team data: ${errorMessage}`
      );
      set({ teamDataError: errorMessage, isTeamDataLoading: false });
    }
  },

  // Fetch operators for a specific team leader instant and team
  fetchTeamOperatorsStatus: async (
    teamLeaderInstantId: string,
    teamId: string
  ) => {
    set({ isStatusMonitoringLoading: true, statusMonitoringError: null });
    try {
      const resp = await api.get<TeamOperatorsStatusResponse>(
        `/visual-check/api/v1/shiftleader/team-instant/${teamLeaderInstantId}/team/${teamId}/operators-status`
      );
      set({
        teamOperatorsStatusData: resp.data,
        isStatusMonitoringLoading: false,
      });
    } catch (error) {
      const errorMessage =
        getApiErrorMessage(error) || "Failed to fetch team operators status";
      set({
        statusMonitoringError: errorMessage,
        isStatusMonitoringLoading: false,
      });
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    }
  },

  // Change team status (REVIEW or VALIDATED)
  changeTeamStatus: async (payload: {
    shiftLeaderInstantId: string;
    teamLeaderInstantId: string;
    teamId: string;
    status: "REVIEW" | "VALIDATED";
    comment?: string;
  }) => {
    try {
      const res = await api.post(
        `/visual-check/api/v1/shiftleader/team-status/change`,
        payload
      );
      if (!res) throw new Error("No response from status change API");
      toast({
        title: "Success",
        description: `Team status changed to ${payload.status}`,
      });
    } catch (error) {
      const errorMessage =
        getApiErrorMessage(error) || "Failed to change team status";
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      throw error;
    }
  },

  fetchShiftLeaderStatusMonitoring: async (endDate: string) => {
    set({ isStatusMonitoringLoading: true, statusMonitoringError: null });

    try {
      const endDateObj = new Date(endDate);
      const startDateObj = new Date(endDateObj);
      startDateObj.setDate(startDateObj.getDate() - 5);
      const startDate = startDateObj.toISOString().split("T")[0];

      const params: Record<string, string> = {
        startDate,
        endDate
      };

      // Fetch the raw API response
      const response = await api.get<ShiftLeaderStatusResponse>(
        `/visual-check/api/v1/shiftleader/status-monitoring`,
        { params }
      );

      console.log(
        "Raw shift leader status monitoring response:",
        response.data
      );

      // Store the complete raw response
      const rawData = response.data;

      // Create the mapped StatusMonitoringResponse for backward compatibility
      const mappedStatusMonitoringData: StatusMonitoringResponse = {
        teams_under_review : rawData.teams_under_review,
        last_date: rawData.last_date,
        shift_instant_id: rawData.shift_instant_id,
        shift_status: "", // not provided by this endpoint
        shift_start: rawData.shift_start,
        shift_end: rawData.shift_end,
        status_data: (rawData.operators_status_data || []).map((op) => ({
          id: op.id,
          legacy_id: op.legacy_id,
          uuid: op.id,
          first_name: op.first_name,
          last_name: op.last_name,
          role: op.function,
          team_id: "",
          dailyRecords: (op.dailyRecords || []).map((dr) => ({
            date: dr.date,
            statusCodes: (dr.statusCodes || []).map((sc) => ({
              code: sc.code,
              startDateTime: sc.startDateTime,
            })),
          })),
        })),
        
      };

      set({

        shiftLeaderStatusResponse: rawData,

        operatorsStatusData: rawData.operators_status_data || [],
        teamLeadersRawStatusData: rawData.teamleaders_status_data || [],

        operatorsShiftTypes: rawData.operators_shift_types || [],


        teamLeadersShiftTypes: rawData.teamleaders_shift_types || [],

        statusMonitoringData: mappedStatusMonitoringData,
        teamLeadersStatusData: (rawData.teamleaders_status_data ||
          []) as unknown as TeamLeaderInstantStatusItem[],

        isStatusMonitoringLoading: false,
      });

      console.log(
        "Stored operators status data:",
        rawData.operators_status_data
      );
      console.log(
        "Stored team leaders status data:",
        rawData.teamleaders_status_data
      );
      console.log("Operators shift types:", rawData.operators_shift_types);
      console.log("Team leaders shift types:", rawData.teamleaders_shift_types);
    } catch (error) {
      const errorMessage =
        getApiErrorMessage(error) ||
        "Failed to fetch shift leader status monitoring data";
      set({
        statusMonitoringError: errorMessage,
        isStatusMonitoringLoading: false,
      });
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    }
  },

  fetchCurrentStatus: async (teamId: string, date: string) => {
    set({ isCurrentStatusLoading: true, currentStatusError: null });

    try {
      const response = await api.get<CurrentStatusResponse>(
        `/api/teams/${teamId}/current`,
        {
          params: { date },
        }
      );

      set({
        currentStatusData: response.data,
        isCurrentStatusLoading: false,
      });
    } catch (error) {
      const errorMessage =
        getApiErrorMessage(error) || "Failed to fetch current status";
      set({
        currentStatusError: errorMessage,
        isCurrentStatusLoading: false,
      });
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    }
  },

  fetchStatusMonitoring: async (
    endDate: string,
    shiftInstantIdParam?: string,
    mfg: boolean = false
  ) => {
    set({ isStatusMonitoringLoading: true, statusMonitoringError: null });

    try {
      // Calculate start date (5 days before end date)
      const endDateObj = new Date(endDate);

      const startDateObj = new Date(endDateObj);
      startDateObj.setDate(startDateObj.getDate() - 2);
      const startDate = startDateObj.toISOString().split("T")[0];

      const params: Record<string, string | boolean> = {
        startDate,
        endDate,
        mfg,
      };

      if (shiftInstantIdParam) {
        params.shiftInstantId = shiftInstantIdParam;
      }

      const response = await api.get<StatusMonitoringResponse>(
        `/visual-check/api/v1/teamleader/status-monitoring`,
        { params }
      );

      set({
        statusMonitoringData: response.data,
        isStatusMonitoringLoading: false,
      });



      // if status is visual check, set visual check timer to 5 minutes
      if (response.data.shift_status === "VISUAL_CHECK") {
        useAttendanceStore.getState().startVisualCheck();
      }


      const teamId = response.data.status_data?.[0]?.team_id;
      const shiftInstantId = response.data.shift_instant_id;
      if (teamId || shiftInstantId) {
        // Add a small delay to ensure SignalR connection is fully ready
        setTimeout(() => {
          get().joinStatusMonitoringGroups(teamId, shiftInstantId);
        }, 1000);
      }
    } catch (error) {
      const errorMessage =
        getApiErrorMessage(error) || "Failed to fetch status monitoring data";
      set({
        statusMonitoringError: errorMessage,
        isStatusMonitoringLoading: false,
      });
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    }
  },

  fetchStatusHistory: async (teamId: string, operatorId: number) => {
    set({ isStatusHistoryLoading: true, statusHistoryError: null });

    try {
      const response = await api.get<StatusHistoryResponse>(
        `/api/shift_instant/${teamId}/operators/${operatorId}/status`
      );

      set((state) => ({
        statusHistory: {
          ...state.statusHistory,
          [operatorId]: response.data.history,
        },
        isStatusHistoryLoading: false,
      }));
    } catch (error) {
      const errorMessage =
        getApiErrorMessage(error) || "Failed to fetch status history";
      set({
        statusHistoryError: errorMessage,
        isStatusHistoryLoading: false,
      });
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    }
  },

  updateOperatorStatus: async (
    shiftInstantId: string,
    operatorId: string,
    status: AttendanceStatus
  ) => {
    set({ isUpdatingStatus: true, updateStatusError: null });

    try {
      const response = await api.post<UpdateStatusResponse>(
        `/visual-check/api/v1/teamleader/shift_instant/${shiftInstantId}/operators/${operatorId}/status`,
        {
          statusCode : status,
        }
      );

      if (response.data.success) {
        // Update the current status data with the new status
        set((state) => ({
          currentStatusData: state.currentStatusData
            ? {
                ...state.currentStatusData,
                operators: state.currentStatusData.operators.map((op) =>
                  op.uuid === operatorId ? { ...op, status } : op
                ),
              }
            : null,
          isUpdatingStatus: false,
        }));

        // Update the status monitoring data locally
        get().updateStatusMonitoringDataLocally(operatorId, status);

        toast({
          title: "Success",
          description: "Operator status updated successfully",
        });
      }
    } catch (error) {
      const errorMessage =
        getApiErrorMessage(error) || "Failed to update operator status";
      set({
        updateStatusError: errorMessage,
        isUpdatingStatus: false,
      });
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    }
  },

  // Getters
  getTeamOperators: () => {
    const { teamData, statusMonitoringData } = get();

    // If we have new status monitoring data, convert it to the old format
    if (statusMonitoringData?.status_data) {
      return statusMonitoringData.status_data.map((operator) => ({
        id: operator.legacy_id,
        mle: operator.legacy_id.toString(),
        firstName: operator.first_name,
        lastName: operator.last_name,
        role: operator.role,
        function: operator.role,
        team: operator.team_id,
        line: undefined,
        status: undefined,
      }));
    }

    return teamData?.operators || [];
  },

  getCurrentStatusForOperator: (operatorId: number) => {
    const { currentStatusData, statusMonitoringData } = get();

    // If we have new status monitoring data, get the latest status
    if (statusMonitoringData?.status_data) {
      const operator = statusMonitoringData.status_data.find(
        (op) => op.legacy_id === operatorId
      );

      if (operator && operator.dailyRecords.length > 0) {
        // Get the latest date's status codes
        const latestRecord =
          operator.dailyRecords[operator.dailyRecords.length - 1];
        if (latestRecord.statusCodes.length > 0) {
          // Get the most recent status code
          const latestStatus =
            latestRecord.statusCodes[latestRecord.statusCodes.length - 1];
          return latestStatus.code as AttendanceStatus;
        }
      }
    }

    // Fallback to old current status data
    const operator = currentStatusData?.operators.find(
      (op) => op.id === operatorId
    );
    return operator?.status || "";
  },

  getStatusHistoryForOperator: (operatorId: number) => {
    const { statusHistory } = get();
    return statusHistory[operatorId] || [];
  },

  getStatusMonitoringOperators: () => {
    const { statusMonitoringData } = get();
    return statusMonitoringData?.status_data || [];
  },

  updateShiftStatus: async (shiftInstantId: string, status: string) => {
    set({ isUpdatingStatus: true, updateStatusError: null });

    try {
      const response = await api.post<UpdateStatusResponse>(
        `/visual-check/api/v1/teamleader/shift_instant/${shiftInstantId}/statuS`,
        {
          shiftStatus: status,
        }
      );

      if (response.data.success) {
        // Update the status monitoring data with the new shift status
        set((state) => ({
          statusMonitoringData: state.statusMonitoringData
            ? {
                ...state.statusMonitoringData,
                shift_status: status,
              }
            : null,
          isUpdatingStatus: false,
        }));

        toast({
          title: "Success",
          description: "Shift status updated successfully",
        });
      } else {
        throw new Error(
          response.data.message || "Failed to update shift status"
        );
      }
    } catch (error) {
      const errorMessage =
        getApiErrorMessage(error) || "Failed to update shift status";
      set({
        updateStatusError: errorMessage,
        isUpdatingStatus: false,
      });

      // Don't show toast here, let the component handle it for better error messages
      // Re-throw the error so the component can handle it
      throw error;
    }
  },

  updateOperatorAttendanceStatus: async (
    shiftInstantId: string,
    operatorId: string,
    statusCode: "P" | "AB"
  ) => {
    set({ isUpdatingStatus: true, updateStatusError: null });

    try {
      const response = await api.post<UpdateStatusResponse>(
        `/visual-check/api/v1/teamleader/shift_instant/${shiftInstantId}/operators/${operatorId}/statuS`,
        {
          statusCode,
        }
      );

      if (response) {
        // Update the status monitoring data locally
        get().updateStatusMonitoringDataLocally(operatorId, statusCode);

        set({ isUpdatingStatus: false });

        toast({
          title: "Success",
          description: "Operator attendance status updated successfully",
        });
      }
    } catch (error) {
      const errorMessage =
        getApiErrorMessage(error) ||
        "Failed to update operator attendance status";
      set({
        updateStatusError: errorMessage,
        isUpdatingStatus: false,
      });
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    }
  },

  updateClerkSubordinateStatus: async (
    clerkInstantId: string,
    subordinateId: string,
    statusCode: string
  ) => {
    set({ isUpdatingStatus: true, updateStatusError: null });

    try {
      const response = await api.post<UpdateStatusResponse>(
        `/visual-check/api/v1/clerks/clerk_instant/${clerkInstantId}/subordinates/${subordinateId}/status`,
        {
          statusCode,
        }
      );

      if (response) {
        // Update the clerk status monitoring data locally
        const currentData = get().clerkStatusMonitoringData;
        if (currentData?.status_data) {
          const today = new Date().toISOString().split("T")[0];
          const updatedStatusData = currentData.status_data.map((subordinate) => {
            if (subordinate.id === subordinateId) {
              // Update or add the daily record for today
              const updatedDailyRecords = subordinate.dailyRecords.map((record) => {
                if (record.date === today) {
                  // Add the new status code to today's record
                  return {
                    ...record,
                    statusCodes: [
                      ...record.statusCodes,
                      {
                        code: statusCode,
                        startDateTime: new Date().toISOString(),
                      },
                    ],
                  };
                }
                return record;
              });

              // If no record for today exists, create one
              const hasToday = updatedDailyRecords.some(
                (record) => record.date === today
              );
              if (!hasToday) {
                updatedDailyRecords.push({
                  date: today,
                  statusCodes: [
                    {
                      code: statusCode,
                      startDateTime: new Date().toISOString(),
                    },
                  ],
                });
              }

              return {
                ...subordinate,
                dailyRecords: updatedDailyRecords,
              };
            }
            return subordinate;
          });

          set({
            clerkStatusMonitoringData: {
              ...currentData,
              status_data: updatedStatusData,
            },
            isUpdatingStatus: false,
          });
        } else {
          set({ isUpdatingStatus: false });
        }

        toast({
          title: "Success",
          description: "Subordinate status updated successfully",
        });
      }
    } catch (error) {
      const errorMessage =
        getApiErrorMessage(error) ||
        "Failed to update subordinate status";
      set({
        updateStatusError: errorMessage,
        isUpdatingStatus: false,
      });
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    }
  },

  updateClerkSubordinateStatusBulk: async (
    clerkInstantId: string,
    statusCode: string,
    subordinateIds: string[]
  ) => {
    set({ isUpdatingStatus: true, updateStatusError: null });

    try {
      const response = await api.post<UpdateStatusResponse>(
        `/visual-check/api/v1/clerks/clerk_instant/${clerkInstantId}/subordinates/status/bulk`,
        {
          statusCode,
          operatorIds :  subordinateIds,
        }
      );

      if (response) {
        // Update the clerk status monitoring data locally for all subordinates
        const currentData = get().clerkStatusMonitoringData;
        if (currentData?.status_data) {
          const today = new Date().toISOString().split("T")[0];
          const updatedStatusData = currentData.status_data.map((subordinate) => {
            if (subordinateIds.includes(subordinate.id)) {
              // Update or add the daily record for today
              const updatedDailyRecords = subordinate.dailyRecords.map((record) => {
                if (record.date === today) {
                  // Add the new status code to today's record
                  return {
                    ...record,
                    statusCodes: [
                      ...record.statusCodes,
                      {
                        code: statusCode,
                        startDateTime: new Date().toISOString(),
                      },
                    ],
                  };
                }
                return record;
              });

              // If no record for today exists, create one
              const hasToday = updatedDailyRecords.some(
                (record) => record.date === today
              );
              if (!hasToday) {
                updatedDailyRecords.push({
                  date: today,
                  statusCodes: [
                    {
                      code: statusCode,
                      startDateTime: new Date().toISOString(),
                    },
                  ],
                });
              }

              return {
                ...subordinate,
                dailyRecords: updatedDailyRecords,
              };
            }
            return subordinate;
          });

          set({
            clerkStatusMonitoringData: {
              ...currentData,
              status_data: updatedStatusData,
            },
            isUpdatingStatus: false,
          });
        } else {
          set({ isUpdatingStatus: false });
        }

        toast({
          title: "Success",
          description: "Bulk subordinate status updated successfully",
        });
      }
    } catch (error) {
      const errorMessage =
        getApiErrorMessage(error) ||
        "Failed to update bulk subordinate status";
      set({
        updateStatusError: errorMessage,
        isUpdatingStatus: false,
      });
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    }
  },

  fetchStatusInfo: async () => {
    set({ isStatusInfoLoading: true, statusInfoError: null });

    try {
      const response = await api.get<StatusInfo[]>(
        "/status-tracking/api/status-info"
      );
      set({
        statusInfoList: response.data,
        isStatusInfoLoading: false,
      });
    } catch (error) {
      const errorMessage =
        getApiErrorMessage(error) || "Failed to fetch status information";
      set({
        statusInfoError: errorMessage,
        isStatusInfoLoading: false,
      });
    }
  },

  fetchOperatorDetails: async (operatorId: string, shiftInstantId: string) => {
    set({ isOperatorDetailsLoading: true, operatorDetailsError: null });

    try {
      const response = await api.get<OperatorDetailsResponse>(
        `/visual-check/api/v1/teamleader/shift_instant/${shiftInstantId}/operators/${operatorId}`
      );
      set((state) => ({
        operatorDetails: {
          ...state.operatorDetails,
          [operatorId]: response.data,
        },
        isOperatorDetailsLoading: false,
      }));
    } catch (error) {
      const errorMessage =
        getApiErrorMessage(error) || "Failed to fetch operator details";
      set({
        operatorDetailsError: errorMessage,
        isOperatorDetailsLoading: false,
      });
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    }
  },

  updateOperatorAttendanceStatusBulk: async (
    shiftInstantId: string,
    statusCode: string,
    operatorIds: string[]
  ) => {
    set({ isUpdatingStatus: true, updateStatusError: null });

    try {
      const response = await api.post<UpdateStatusResponse>(
        `visual-check/api/v1/teamleader/shift_instant/${shiftInstantId}/operators/status/bulk`,
        {
          statusCode,
          operatorIds,
        }
      );

      if (response) {
        // Update the status monitoring data locally for all operators
        operatorIds.forEach(operatorId => {
          get().updateStatusMonitoringDataLocally(operatorId, statusCode);
        });

        set({ isUpdatingStatus: false });

        toast({
          title: "Success",
          description: "Bulk operator status updated successfully",
        });
      }
    } catch (error) {
      const errorMessage =
        getApiErrorMessage(error) || "Failed to update bulk operator status";
      set({
        updateStatusError: errorMessage,
        isUpdatingStatus: false,
      });
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    }
  },

  fetchShiftTypes: async (date: Date) => {
    set({ isShiftTypesLoading: true, shiftTypesError: null });

    try {
      const dateStr =     new Date(date.getTime() - (date.getTimezoneOffset() * 60000))
  .toISOString()
  .split('T')[0];
      const response = await api.get<{
        shift_types: Array<{ shift_type: string; shift_instant_id: string ;designation : string}>;
      }>(`/visual-check/api/v1/teamleader/shift-types?date=${dateStr}`);



      console.log("Fetched shift types:", response.data.shift_types);
      const newShiftTypes = (response.data.shift_types || []).map((st) => ({
        shiftType: st.shift_type,
        shiftInstantId: st.shift_instant_id,
        designation : st.designation
      }));

      console.log("Mapped shift types:", newShiftTypes);

      set({
        shiftTypes: newShiftTypes,
        isShiftTypesLoading: false,
      });

      return newShiftTypes;
    } catch (error) {
      const errorMessage =
        getApiErrorMessage(error) || "Failed to fetch shift types";
      set({
        shiftTypesError: errorMessage,
        isShiftTypesLoading: false,
      });
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      throw error;
    }
  },

  // DEPARTMENT_CLERK methods
  fetchClerkShiftTypes: async (date: Date) => {
    set({ isClerkShiftTypesLoading: true, clerkShiftTypesError: null });

    try {
      const dateStr = new Date(date.getTime() - (date.getTimezoneOffset() * 60000))
  .toISOString()
  .split('T')[0];
      const response = await api.get<ClerkShiftTypesResponse>(
        `/visual-check/api/v1/clerks/shift-types?date=${dateStr}`
      );

      const clerkShiftTypes = response.data.shift_types || [];

      console.log("Fetched clerk shift types:", clerkShiftTypes);

      set({
        clerkShiftTypes,
        isClerkShiftTypesLoading: false,
      });

      return clerkShiftTypes;
    } catch (error) {
      const errorMessage =
        getApiErrorMessage(error) || "Failed to fetch clerk shift types";
      set({
        clerkShiftTypesError: errorMessage,
        isClerkShiftTypesLoading: false,
      });
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      throw error;
    }
  },

  fetchClerkStatusMonitoring: async (date: Date, clerkInstantId?: string) => {
    set({ isClerkStatusMonitoringLoading: true, clerkStatusMonitoringError: null });

    try {
      
      console.log('fetchClerkStatusMonitoring' , date )
      const dateStr = new Date(date.getTime() - (date.getTimezoneOffset() * 60000))
  .toISOString()
  .split('T')[0];

      console.log('fetchClerkStatusMonitoring' , dateStr )
      let url = `/visual-check/api/v1/clerks/status-monitoring?startDate=${dateStr}`;

      if (clerkInstantId) {
        url += `&clerkInstantId=${clerkInstantId}`;
      }

      

      const response = await api.get<ClerkStatusMonitoringResponse>(url);

      console.log("Fetched clerk status monitoring data:", response.data);

      set({
        clerkStatusMonitoringData: response.data,
        isClerkStatusMonitoringLoading: false,
      });
    } catch (error) {
      const errorMessage =
        getApiErrorMessage(error) || "Failed to fetch clerk status monitoring";
      set({
        clerkStatusMonitoringError: errorMessage,
        isClerkStatusMonitoringLoading: false,
      });
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      throw error;
    }
  },

  approveClerkShift: async (clerkInstantId: string, employeeType: string) => {
    try {
      await api.post(
        `/visual-check/api/v1/clerks/clerk_instant/${clerkInstantId}/${employeeType}/status`,
        {
          comment: "",
          ShiftStatus: "APPROVED"
        }
      );

      toast({
        title: "Success",
        description: "Shift approved successfully",
        variant: "default",
      });

      // update locally

      get().updateClerkStatusMonitoringDataLocally(clerkInstantId, employeeType, "APPROVED");



    } catch (error) {
      const errorMessage =
        getApiErrorMessage(error) || "Failed to approve shift";

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });

      throw error;
    }
  },

  updateClerkStatusMonitoringDataLocally: (clerkInstantId: string, employeeType: string, status: string) => {
    const currentData = get().clerkStatusMonitoringData;  
    if (currentData) {
      if (employeeType === "IH") {
        set({
          clerkStatusMonitoringData: {
            ...currentData,
            shift_status_IH: status,
          },
        });
      } else {
        set({
          clerkStatusMonitoringData: {
            ...currentData,
            shift_status_IS: status,
          },
        });
      }
    }
  },
  
  
  fetchManagerShiftTypes: async (date: Date) => {
    set({ isManagerShiftTypesLoading: true, managerShiftTypesError: null });

    try {
      const dateStr = new Date(date.getTime() - (date.getTimezoneOffset() * 60000))
        .toISOString()
        .split('T')[0];

      const response = await api.get<{
        count: number;
        shift_types: ClerkShiftType[];
        date: string;
        clerk_id: string;
      }>(`/visual-check/api/v1/manager/shift-types?date=${dateStr}`);

      console.log("Fetched manager shift types:", response.data);

      const newShiftTypes = response.data.shift_types || [];
      set({
        managerShiftTypes: newShiftTypes,
        isManagerShiftTypesLoading: false,
      });

      return newShiftTypes;
    } catch (error) {
      const errorMessage =
        getApiErrorMessage(error) || "Failed to fetch manager shift types";
      set({
        managerShiftTypesError: errorMessage,
        isManagerShiftTypesLoading: false,
      });
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      throw error;
    }
  },

  fetchManagerStatusMonitoring: async (date: Date, clerkInstantId?: string) => {
    set({ isManagerStatusMonitoringLoading: true, managerStatusMonitoringError: null });

    try {
      console.log('fetchManagerStatusMonitoring', date);
      const dateStr = new Date(date.getTime() - (date.getTimezoneOffset() * 60000))
        .toISOString()
        .split('T')[0];

      console.log('fetchManagerStatusMonitoring', dateStr);
      let url = `/visual-check/api/v1/manager/status-monitoring?startDate=${dateStr}`;

      if (clerkInstantId) {
        url += `&clerkInstantId=${clerkInstantId}`;
      }

      const response = await api.get<ClerkStatusMonitoringResponse>(url);

      console.log("Fetched manager status monitoring data:", response.data);

      set({
        managerStatusMonitoringData: response.data,
        isManagerStatusMonitoringLoading: false,
      });
    } catch (error) {
      const errorMessage =
        getApiErrorMessage(error) || "Failed to fetch manager status monitoring";
      set({
        managerStatusMonitoringError: errorMessage,
        isManagerStatusMonitoringLoading: false,
      });
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      throw error;
    }
  },

  fetchManagerSubordinateDetails: async (clerkInstantId: string, subordinateId: string) => {
    try {
      const response = await api.get(
        `/visual-check/api/v1/manager/clerk_instant/${clerkInstantId}/subordinates/${subordinateId}`
      );

      console.log("Fetched manager subordinate details:", response.data);
      return response.data;
    } catch (error) {
      const errorMessage =
        getApiErrorMessage(error) || "Failed to fetch subordinate details";
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      throw error;
    }
  },

  approveManagerShift: async (clerkInstantId: string, employeeType: string) => {
    try {
      await api.post(
        `/visual-check/api/v1/manager/clerk_instant/${clerkInstantId}/${employeeType}/status`,
        {
          comment: "",
          ShiftStatus: "VALIDATED"
        }
      );

      toast({
        title: "Success",
        description: "Shift validated successfully",
        variant: "default",
      });


      get().updateManagerStatusMonitoringDataLocally(clerkInstantId, employeeType, "VALIDATED");
    } catch (error) {
      const errorMessage =
        getApiErrorMessage(error) || "Failed to validate shift";

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });

      throw error;
    }
  },

  updateManagerStatusMonitoringDataLocally: (clerkInstantId: string, employeeType: string, status: string) => {
    const currentData = get().managerStatusMonitoringData;  
    if (currentData) {
      if (employeeType === "IH") {
        set({
          managerStatusMonitoringData: {
            ...currentData,
            shift_status_IH: status,
          },
        });
      }
      else {
        set({
          managerStatusMonitoringData: {
            ...currentData,
            shift_status_IS: status,
          },
        });
      }
    }
  },


  sendForReviewManagerShift: async (clerkInstantId: string, employeeType: string , comment : string) => {


    try {
      await api.post(
        `/visual-check/api/v1/manager/clerk_instant/${clerkInstantId}/${employeeType}/status`,
        {
          comment,
          ShiftStatus: "REVIEW"
        }
      );

      toast({
        title: "Success",
        description: "Shift Sent for Review  successfully",
        variant: "default",
      });

      get().updateManagerStatusMonitoringDataLocally(clerkInstantId, employeeType, "REVIEW");
    } catch (error) {
      const errorMessage =
        getApiErrorMessage(error) || "Failed to validate shift";

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });

      throw error;
    }
  },

  // SignalR methods
  connectToStatusMonitoring: async () => {
    try {
      await signalRService.connectToStatusMonitoring();
      console.log(
        `${new Date().toLocaleTimeString()} - Team leader store connected to status monitoring hub`
      );

      // Set up listeners after successful connection
      get().setupStatusMonitoringListeners();
    } catch (error) {
      const errorMessage =
        error && typeof error === "object" && "message" in error
          ? (error as Error).message
          : String(error);
      console.log(
        `${new Date().toLocaleTimeString()} - Team leader store failed to connect to status monitoring: ${errorMessage}`
      );

      // Show error toast
      toast({
        title: "Connection Error",
        description:
          "Failed to connect to status monitoring. Real-time updates may not work.",
        variant: "destructive",
      });
    }
  },

  disconnectFromStatusMonitoring: async () => {
    try {
      await signalRService.disconnectFromStatusMonitoring();
      signalRService.resetStatusMonitoringCallbacks();
      set({ statusMonitoringListenersSetup: false }); // Reset the flag
      console.log(
        `${new Date().toLocaleTimeString()} - Team leader store disconnected from status monitoring`
      );
    } catch (error) {
      const errorMessage =
        error && typeof error === "object" && "message" in error
          ? (error as Error).message
          : String(error);
      console.log(
        `${new Date().toLocaleTimeString()} - Error disconnecting from status monitoring: ${errorMessage}`
      );
    }
  },

  joinStatusMonitoringGroups: async (
    teamId?: string,
    shiftInstantId?: string
  ) => {
    const state = get();
    const currentTeamId =
      teamId || state.statusMonitoringData?.status_data?.[0]?.team_id;
    const currentShiftInstantId =
      shiftInstantId || state.statusMonitoringData?.shift_instant_id;

    // Wait for connection to be ready (similar to index.js pattern)
    let retries = 0;
    const maxRetries = 10;
    while (
      !signalRService.isStatusMonitoringConnected() &&
      retries < maxRetries
    ) {
      console.log(
        `${new Date().toLocaleTimeString()} - Waiting for status monitoring connection... (${retries + 1}/${maxRetries})`
      );
      await new Promise((resolve) => setTimeout(resolve, 500));
      retries++;
    }

    if (!signalRService.isStatusMonitoringConnected()) {
      console.log(
        `${new Date().toLocaleTimeString()} - Status monitoring connection not ready after ${maxRetries} retries`
      );
      return;
    }

    try {
      if (currentTeamId) {
        await signalRService.joinTeamGroup(currentTeamId);
      }

      if (currentShiftInstantId) {
        await signalRService.joinShiftInstantGroup(currentShiftInstantId);
      }
    } catch (error) {
      const errorMessage =
        error && typeof error === "object" && "message" in error
          ? (error as Error).message
          : String(error);
      console.log(
        `${new Date().toLocaleTimeString()} - Error joining status monitoring groups: ${errorMessage}`
      );
    }
  },

  leaveStatusMonitoringGroups: async (
    teamId?: string,
    shiftInstantId?: string
  ) => {
    const state = get();
    const currentTeamId =
      teamId || state.statusMonitoringData?.status_data?.[0]?.team_id;
    const currentShiftInstantId =
      shiftInstantId || state.statusMonitoringData?.shift_instant_id;

    try {
      if (currentTeamId) {
        await signalRService.leaveTeamGroup(currentTeamId);
      }

      if (currentShiftInstantId) {
        await signalRService.leaveShiftInstantGroup(currentShiftInstantId);
      }
    } catch (error) {
      const errorMessage =
        error && typeof error === "object" && "message" in error
          ? (error as Error).message
          : String(error);
      console.log(
        `${new Date().toLocaleTimeString()} - Error leaving status monitoring groups: ${errorMessage}`
      );
    }
  },

  setupStatusMonitoringListeners: () => {
    const state = get();

    // Prevent duplicate listener setup
    if (state.statusMonitoringListenersSetup) {
      console.log(
        `${new Date().toLocaleTimeString()} - Status monitoring listeners already set up, skipping...`
      );
      return;
    }

    console.log(
      `${new Date().toLocaleTimeString()} - Setting up status monitoring listeners...`
    );
    set({ statusMonitoringListenersSetup: true });

    // Listen for operator status updates
    signalRService.onOperatorStatusUpdate((update: OperatorStatusUpdate) => {
      console.log(
        `${new Date().toLocaleTimeString()} - OperatorStatusUpdate: ${JSON.stringify(update)}`
      );

      // Update the status monitoring data with the new status
      const currentData = get().statusMonitoringData;

      if (currentData?.status_data) {
        const updatedStatusData = currentData.status_data.map((operator) => {
          console.log("operator", operator);
          if (operator.id == update.operatorId) {
            // Find today's date

            console.log("found operator", operator);
            const today = new Date().toISOString().split("T")[0];

            // Update or add the daily record for today
            const updatedDailyRecords = operator.dailyRecords.map((record) => {
              if (record.date === today) {
                // Add the new status code to today's record
                return {
                  ...record,
                  statusCodes: [
                    ...record.statusCodes,
                    {
                      code: update.statusCode,
                      startDateTime: update.timestamp,
                    },
                  ],
                };
              }
              return record;
            });

            // If no record for today exists, create one
            const hasToday = updatedDailyRecords.some(
              (record) => record.date === today
            );
            if (!hasToday) {
              updatedDailyRecords.push({
                date: today,
                statusCodes: [
                  {
                    code: update.statusCode,
                    startDateTime: update.timestamp,
                  },
                ],
              });
            }

            return {
              ...operator,
              dailyRecords: updatedDailyRecords,
            };
          }
          return operator;
        });

        console.log("updatedStatusData after update", updatedStatusData);
        set({
          statusMonitoringData: {
            ...currentData,
            status_data: updatedStatusData,
          },
        });

        // Show toast notification
        toast({
          title: "Status Updated",
          description: `Operator ${update.operatorId} status changed to ${update.statusCode}`,
        });
      }


      const currentClerkData = get().clerkStatusMonitoringData;

      console.warn("currentData_clerk", currentClerkData);
      console.warn("all_store_state", get());


      // fetch clerk data again
      get().fetchClerkStatusMonitoring(new Date() , currentClerkData?.clerk_shift_instant_id);
      

      // if (currentClerkData?.status_data) {
      //   const today = new Date().toISOString().split("T")[0];
        
      //   console.log('todaty ', today)
      //   const updatedClerkStatusData = currentClerkData.status_data.map(
      //     (subordinate) => {
      //       if (subordinate.id === update.operatorId) {
      //         // Update or add the daily record for today
      //         const updatedDailyRecords = subordinate.dailyRecords.map((record) => {
      //           if (record.date === today) {
      //             // Add the new status code to today's record
      //             return {
      //               ...record,
      //               statusCodes: [
      //                 ...record.statusCodes,
      //                 {
      //                   code: update.statusCode,
      //                   startDateTime: update.timestamp, // Use updatedAt from SignalR
      //                 },
      //               ],
      //             };
      //           }
      //           return record;
      //         });

      //         // If no record for today exists, create one
      //         const hasToday = updatedDailyRecords.some(
      //           (record) => record.date === today
      //         );
      //         if (!hasToday) {
      //           updatedDailyRecords.push({
      //             date: today,
      //             statusCodes: [
      //               {
      //                 code: update.statusCode,
      //                 startDateTime: update.timestamp,
      //               },
      //             ],
      //           });
      //         }

      //         return {
      //           ...subordinate,
      //           dailyRecords: updatedDailyRecords,
      //         };
      //       }
      //       return subordinate;
      //     }
      //   );
        
      //   set({
      //     clerkStatusMonitoringData: {
      //       ...currentClerkData,
      //       status_data: updatedClerkStatusData,
      //     },
      //   });
      // }

    });

    // Listen for shift instant status updates
    signalRService.onShiftInstantStatusUpdate(
      (update: ShiftInstantStatusUpdate) => {
        console.log(
          `${new Date().toLocaleTimeString()} - ShiftInstantStatusUpdate: ${JSON.stringify(update)}`
        );

        const currentData = get().statusMonitoringData;
        if (currentData) {
          set({
            statusMonitoringData: {
              ...currentData,
              shift_status: update.currentStatus,
            },
          });

          toast({
            title: "Shift Status Updated",
            description: `Shift status changed from ${update.previousStatus} to ${update.currentStatus}`,
          });
        }
      }
    );

    // Listen for team status monitoring updates
    signalRService.onTeamStatusMonitoringUpdate(
      (update: TeamStatusMonitoringUpdate) => {
        console.log(
          `${new Date().toLocaleTimeString()} - TeamStatusMonitoringUpdate: ${JSON.stringify(update)}`
        );

        // Refresh the status monitoring data
        if (state.statusMonitoringData) {
          const endDate = new Date().toISOString().split("T")[0];
          get().fetchStatusMonitoring(endDate);
        }

        toast({
          title: "Team Data Updated",
          description: "Team status monitoring data has been refreshed",
        });
      }
    );
  },

  // Clear actions
  clearTeamData: () => set({ teamData: null, teamDataError: null }),
  clearCurrentStatus: () =>
    set({ currentStatusData: null, currentStatusError: null }),
  clearStatusMonitoring: () =>
    set({ statusMonitoringData: null, statusMonitoringError: null }),
  clearStatusHistory: () =>
    set({ statusHistory: {}, statusHistoryError: null }),
  clearErrors: () => {
    set({
      teamDataError: null,
      currentStatusError: null,
      statusMonitoringError: null,
      statusHistoryError: null,
      updateStatusError: null,
      statusInfoError: null,
      operatorDetailsError: null,
      shiftTypesError: null,
    });
  },

  clearShiftTypes: () => {
    set({
      shiftTypes: [],
      isShiftTypesLoading: false,
      shiftTypesError: null,
    });
  },

  getOperatorsStatusData: () => {
    const { operatorsStatusData } = get();
    return operatorsStatusData || [];
  },

  getTeamLeadersStatusData: () => {
    const { teamLeadersRawStatusData } = get();
    return teamLeadersRawStatusData || [];
  },

  clearShiftLeaderStatusData: () => {
    set({
      shiftLeaderStatusResponse: null,
      operatorsStatusData: null,
      teamLeadersRawStatusData: null,
      operatorsShiftTypes: [],
      teamLeadersShiftTypes: [],
    });
  },

  // Local update methods
  updateStatusMonitoringDataLocally: (operatorId: string, statusCode: string) => {
    const currentData = get().statusMonitoringData;
    if (currentData?.status_data) {
      const today = new Date().toISOString().split("T")[0];
      const updatedStatusData = currentData.status_data.map((operator) => {
        if (operator.id === operatorId) {
          // Update or add the daily record for today
          const updatedDailyRecords = operator.dailyRecords.map((record) => {
            if (record.date === today) {
              // Add the new status code to today's record
              return {
                ...record,
                statusCodes: [
                  ...record.statusCodes,
                  {
                    code: statusCode,
                    startDateTime: new Date().toISOString(),
                  },
                ],
              };
            }
            return record;
          });

          // If no record for today exists, create one
          const hasToday = updatedDailyRecords.some(
            (record) => record.date === today
          );
          if (!hasToday) {
            updatedDailyRecords.push({
              date: today,
              statusCodes: [
                {
                  code: statusCode,
                  startDateTime: new Date().toISOString(),
                },
              ],
            });
          }

          return {
            ...operator,
            dailyRecords: updatedDailyRecords,
          };
        }
        return operator;
      });

      set({
        statusMonitoringData: {
          ...currentData,
          status_data: updatedStatusData,
        },
      });
    }
  },

  updateTeamLeadersStatusDataLocally: (teamLeaderId: number, statusCode: string) => {
    const currentData = get().teamLeadersStatusData;
    if (currentData) {
      const today = new Date().toISOString().split("T")[0];
      const updatedTeamLeaders = currentData.map((teamLeader) => {
        if (teamLeader.legacy_id === teamLeaderId) {
          // Update or add the daily record for today
          const updatedDailyRecords = teamLeader.dailyRecords.map((record) => {
            if (record.date === today) {
              // Add the new status code to today's record
              return {
                ...record,
                statusCodes: [
                  ...record.statusCodes,
                  {
                    code: statusCode,
                    startDateTime: new Date().toISOString(),
                  },
                ],
              };
            }
            return record;
          });

          // If no record for today exists, create one
          const hasToday = updatedDailyRecords.some(
            (record) => record.date === today
          );
          if (!hasToday) {
            updatedDailyRecords.push({
              date: today,
              statusCodes: [
                {
                  code: statusCode,
                  startDateTime: new Date().toISOString(),
                },
              ],
            });
          }

          return {
            ...teamLeader,
            dailyRecords: updatedDailyRecords,
          };
        }
        return teamLeader;
      });

      set({
        teamLeadersStatusData: updatedTeamLeaders,
      });
    }
  },

  // QUALITY_SUPERVISOR functions
  fetchQualityShiftTypes: async (date: Date) => {
    set({ isQualityShiftTypesLoading: true, qualityShiftTypesError: null });

    try {
      const dateStr = new Date(date.getTime() - (date.getTimezoneOffset() * 60000))
        .toISOString()
        .split('T')[0];

      const response = await api.get<QualityShiftTypesResponse>(
        `/visual-check/api/v1/quality/shift-types?date=${dateStr}`
      );

      console.log("Fetched quality shift types:", response.data);

      const newShiftTypes = response.data.shift_types || [];
      set({
        qualityShiftTypes: newShiftTypes,
        isQualityShiftTypesLoading: false,
      });

      return newShiftTypes;
    } catch (error) {
      const errorMessage =
        getApiErrorMessage(error) || "Failed to fetch quality shift types";
      set({
        qualityShiftTypesError: errorMessage,
        isQualityShiftTypesLoading: false,
      });
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      throw error;
    }
  },

  fetchQualityStatusMonitoring: async (date: Date, qualityInstantId?: string) => {
    set({ isQualityStatusMonitoringLoading: true, qualityStatusMonitoringError: null });

    try {
      console.log('fetchQualityStatusMonitoring', date);
      const dateStr = new Date(date.getTime() - (date.getTimezoneOffset() * 60000))
        .toISOString()
        .split('T')[0];

      const params: Record<string, string> = {
        startDate: dateStr,
        endDate: dateStr,
      };

      if (qualityInstantId) {
        params.qualityInstantId = qualityInstantId;
      }

      const response = await api.get<QualityStatusMonitoringResponse>(
        `/visual-check/api/v1/quality/status-monitoring`,
        { params }
      );

      console.log("Fetched quality status monitoring:", response.data);

      set({
        qualityStatusMonitoringData: response.data,
        isQualityStatusMonitoringLoading: false,
      });
    } catch (error) {
      const errorMessage =
        getApiErrorMessage(error) || "Failed to fetch quality status monitoring";
      set({
        qualityStatusMonitoringError: errorMessage,
        isQualityStatusMonitoringLoading: false,
      });
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      throw error;
    }
  },

  updateQualitySubordinateStatus: async (
    qualityInstantId: string,
    subordinateId: string,
    statusCode: string
  ) => {
    try {
      await api.post(
        `/visual-check/api/v1/quality/quality_instant/${qualityInstantId}/subordinates/${subordinateId}/status`,
        { statusCode }
      );

      toast({
        title: "Success",
        description: "Subordinate status updated successfully",
        variant: "default",
      });

      // Refresh the data after successful update
      const currentDate = new Date();
      await get().fetchQualityStatusMonitoring(currentDate, qualityInstantId);
    } catch (error) {
      const errorMessage =
        getApiErrorMessage(error) || "Failed to update subordinate status";
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      throw error;
    }
  },

  updateQualitySubordinateStatusBulk: async (
    qualityInstantId: string,
    statusCode: string,
    subordinateIds: string[]
  ) => {
    try {
      await api.post(
        `/visual-check/api/v1/quality/quality_instant/${qualityInstantId}/subordinates/status/bulk`,
        {
          statusCode,
          operatorIds: subordinateIds,
        }
      );

      toast({
        title: "Success",
        description: "Bulk subordinate status updated successfully",
        variant: "default",
      });

      // Refresh the data after successful update
      const currentDate = new Date();
      await get().fetchQualityStatusMonitoring(currentDate, qualityInstantId);
    } catch (error) {
      const errorMessage =
        getApiErrorMessage(error) || "Failed to update bulk subordinate status";
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      throw error;
    }
  },

  approveQualityShift: async (qualityInstantId: string, shiftStatus: string = "APPROVED") => {
    try {
      await api.post(
        `/visual-check/api/v1/quality/quality_instant/${qualityInstantId}/status`,
        { ShiftStatus: shiftStatus }
      );

      const actionText = shiftStatus === "VISUAL_CHECK" ? "Visual check started" :
                        shiftStatus === "VALIDATED" ? "Visual check completed" :
                        "Quality shift approved";

      toast({
        title: "Success",
        description: `${actionText} successfully`,
        variant: "default",
      });

      // Refresh the data after successful update
      const currentDate = new Date();
      await get().fetchQualityStatusMonitoring(currentDate, qualityInstantId);
    } catch (error) {
      const errorMessage =
        getApiErrorMessage(error) || "Failed to update quality shift status";
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      throw error;
    }
  },
}));
