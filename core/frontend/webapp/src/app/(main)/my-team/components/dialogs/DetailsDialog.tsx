"use client";

import { useState, useMemo, useEffect } from "react";

import { Separator } from "@/components/ui/separator";
import { Button } from "@/components/ui/button";
import { ReusableDialog } from "@/components/common/CustomDialog";
import {
  formatDateToYYYYMMDD,
  adjustOverlappingTimeSlots,
  parseTime,
  STATUS_MAP,
  calculateDurationInMinutes,
} from "../../types/Types";
import { AddTimeSlotForm } from "../AddTimeSlots";
import { TimeSlotSummary } from "../TimeSlotSummary";
import { TimeSlotListItem } from "../TimeSlotItem";
import { TimelineView } from "../TimeLineView";
import { useTeamLeaderStore } from "../../store/teamLeaderStore";

interface TimeSlot {
  id: string;
  date: string; // YYYY-MM-DD
  start: string; // HH:MM
  end: string; // HH:MM
  status: string;
  duration: string;
}

interface DetailsDialogProps {
  isOpen?: boolean;
  onClose?: () => void;
  worker?: { id: string; name: string; mle?: string };
  date?: Date;
  operatorId?: string;
  shiftInstantId?: string;
  onDataRefresh?: () => void; // Optional callback to refresh parent data
}

export function DetailsDialog({
  isOpen: externalIsOpen = true,
  onClose: externalOnClose,
  date: externalDate,
  worker,
  operatorId,
  shiftInstantId,
  onDataRefresh,
}: DetailsDialogProps = {}) {
  const [isOpen, setIsOpen] = useState(externalIsOpen);
  const [selectedDate, setSelectedDate] = useState<Date>(
    externalDate || new Date(),
  );

  // API integration for operator details
  const {
    operatorDetails,
    isOperatorDetailsLoading,
    operatorDetailsError,
    fetchOperatorDetails,
    addManualTimeInterval,
  } = useTeamLeaderStore();

  const operatorData = operatorId ? operatorDetails[operatorId] : null;

  // Convert API data to time slots format
  const apiTimeSlots = useMemo(() => {
    if (!operatorData?.status_history) return [];

    return operatorData.status_history.map((status, index) => {
      const startTime = new Date(status.activatedAt);
      const endTime = status.endedAt ? new Date(status.endedAt) : new Date();

      // Calculate duration in minutes
      const durationMinutes = Math.floor((endTime.getTime() - startTime.getTime()) / (1000 * 60));

      return {
        id: `api-${index}`,
        date: formatDateToYYYYMMDD(startTime), // Add required date field
        start: startTime.toTimeString().slice(0, 5), // HH:MM format
        end: endTime.toTimeString().slice(0, 5), // HH:MM format
        duration: `${Math.floor(durationMinutes / 60)}h ${durationMinutes % 60}m`,
        status: status.code,
      };
    });
  }, [operatorData]);



  // Sync external props with internal state
  useEffect(() => {
    setIsOpen(externalIsOpen);
  }, [externalIsOpen]);

  useEffect(() => {
    if (externalDate) {
      setSelectedDate(externalDate);
    }
  }, [externalDate]);

  // Fetch operator details when dialog opens
  useEffect(() => {
    if (isOpen && operatorId && shiftInstantId && !operatorData) {
      fetchOperatorDetails(operatorId, shiftInstantId);
    }
  }, [isOpen, operatorId, shiftInstantId, operatorData, fetchOperatorDetails]);

  const handleClose = () => {
    setIsOpen(false);
    if (externalOnClose) {
      externalOnClose();
    }
  };



  const [timeSlots, setTimeSlots] = useState<TimeSlot[]>([
    {
      id: "1",
      date: formatDateToYYYYMMDD(new Date()),
      start: "09:00",
      end: "09:30", // 30 minutes
      status: "F",
      duration: "30min",
    },
    {
      id: "2",
      date: formatDateToYYYYMMDD(new Date()),
      start: "09:30",
      end: "09:45", // 15 minutes
      status: "P",
      duration: "15min",
    },
    {
      id: "3",
      date: formatDateToYYYYMMDD(new Date()),
      start: "09:45",
      end: "10:30", // 45 minutes
      status: "P in lan",
      duration: "45min",
    },
    {
      id: "4",
      date: formatDateToYYYYMMDD(new Date()),
      start: "10:30",
      end: "12:00", // 90 minutes
      status: "CTP",
      duration: "1h 30min",
    },
    {
      id: "5",
      date: formatDateToYYYYMMDD(new Date()),
      start: "13:00",
      end: "15:30", // 150 minutes
      status: "P",
      duration: "2h 30min",
    },
    {
      id: "6",
      date: formatDateToYYYYMMDD(new Date()),
      start: "15:30",
      end: "16:00", // 30 minutes
      status: "DN",
      duration: "30min",
    },
    // Add some slots for a different day to demonstrate filtering
    {
      id: "7",
      date: formatDateToYYYYMMDD(
        new Date(new Date().setDate(new Date().getDate() + 1)),
      ), // Tomorrow
      start: "08:00",
      end: "09:00",
      status: "CR",
      duration: "1h",
    },
  ]);

  const legendItems = Object.entries(STATUS_MAP).filter(
    ([key]) => key !== "Active",
  ); // Exclude 'Active' from main legend

  const handleAddTimeSlot = async (
    start: string,
    end: string,
    status: string,
    duration: string,
  ) => {



    console.log('operatorId' , operatorId , 'shiftInstantId' ,shiftInstantId)
    if (operatorId && shiftInstantId) {
      try {
        // Convert time strings to full datetime strings
        const selectedDateStr = formatDateToYYYYMMDD(selectedDate);
        const startDateTime = `${selectedDateStr}T${start}:00`;
        const endDateTime = `${selectedDateStr}T${end}:00`;

        await addManualTimeInterval(
          shiftInstantId,
          operatorId,
          status,
          startDateTime,
          endDateTime
        );

        // The API call will automatically refresh the operator details
        // Also trigger parent data refresh if callback is provided
        if (onDataRefresh) {
          onDataRefresh();
        }
      } catch (error) {
        console.error("Failed to add manual time interval:", error);
        // Error is already handled in the store with toast
      }
    } else {
      // Fallback to local state management for non-API data
      const newSlot: TimeSlot = {
        id: String(Date.now()), // Simple unique ID
        date: formatDateToYYYYMMDD(selectedDate), // Assign the currently selected date
        start,
        end,
        status,
        duration,
      };

      setTimeSlots((prev) => {
        // Adjust overlapping time slots
        const adjustedSlots = adjustOverlappingTimeSlots(prev, {
          start: newSlot.start,
          end: newSlot.end,
          date: newSlot.date,
        });

        // Add the new slot and sort by date then by start time
        const updatedSlots = [...adjustedSlots, newSlot].sort((a, b) => {
          const dateA = new Date(a.date);
          const dateB = new Date(b.date);
          if (dateA.getTime() !== dateB.getTime()) {
            return dateA.getTime() - dateB.getTime();
          }
          return parseTime(a.start) - parseTime(b.start);
        });

        return updatedSlots;
      });
    }
  };

  const handleRemoveTimeSlot = (id: string) => {
    setTimeSlots((prev) => prev.filter((slot) => slot.id !== id));
  };

  const filteredTimeSlots = useMemo(() => {
    const selectedDateString = formatDateToYYYYMMDD(selectedDate);
    return timeSlots.filter((slot) => slot.date === selectedDateString);
  }, [timeSlots, selectedDate]);

  // Use API time slots if available, otherwise use existing time slots
  const displayTimeSlots = operatorData ? apiTimeSlots : filteredTimeSlots;

  const minStartTimeForNewSlot = useMemo(() => {
    if (filteredTimeSlots.length === 0) {
      // If no slots for the day, allow starting from 00:00 of the selected date
      const startOfDay = new Date(selectedDate);
      startOfDay.setHours(0, 0, 0, 0);
      return startOfDay;
    }
    // Allow starting from any time (overlaps will be handled automatically)
    const startOfDay = new Date(selectedDate);
    startOfDay.setHours(0, 0, 0, 0);
    return startOfDay;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedDate]);

  const timeSlotSummaryData = useMemo(() => {
    // Use API data if available
    if (operatorData?.status_time_summary) {
      return operatorData.status_time_summary.map((summary) => ({
        status: summary.status_description,
        totalDurationMinutes: summary.total_time_minutes,
        count: 1, // API doesn't provide count, so default to 1
        color: STATUS_MAP[summary.status_code]?.color || "bg-gray-400",
      }));
    }

    // Fallback to existing logic for non-API data
    const summary: {
      [key: string]: {
        totalDurationMinutes: number;
        count: number;
        color: string;
        name: string;
      };
    } = {};

    displayTimeSlots.forEach((slot) => {
      const statusInfo = STATUS_MAP[slot.status] || {
        name: slot.status,
        color: "bg-gray-400",
      };
      const durationMinutes = calculateDurationInMinutes(slot.start, slot.end);

      if (!summary[slot.status]) {
        summary[slot.status] = {
          totalDurationMinutes: 0,
          count: 0,
          color: statusInfo.color,
          name: statusInfo.name,
        };
      }
      summary[slot.status].totalDurationMinutes += durationMinutes;
      summary[slot.status].count += 1;
    });

    return Object.values(summary).map((item) => ({
      status: item.name,
      totalDurationMinutes: item.totalDurationMinutes,
      count: item.count,
      color: item.color,
    }));
  }, [operatorData, displayTimeSlots]);

  const footer = (
    <div className="flex justify-end gap-2">
      <Button variant="outline" onClick={handleClose}>
        Cancel
      </Button>
      <Button type="submit">Submit</Button>
    </div>
  );

  return (
    <ReusableDialog
      isOpen={isOpen}
      onClose={handleClose}
      title={operatorData ? `Operator Details - ${worker?.name || 'Unknown'}` : "Attendance sheet"}
      size="5xl"
      footer={footer}
    >
      <div className="grid gap-6 py-4">
        {/* Show loading state for API data */}
        {isOperatorDetailsLoading && operatorId && (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="ml-2">Loading operator details...</span>
          </div>
        )}

        {/* Show error state for API data */}
        {operatorDetailsError && operatorId && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <p className="text-red-800">Error: {operatorDetailsError}</p>
          </div>
        )}

        {/* Always show the existing time slots UI, but use API data when available */}
        {!isOperatorDetailsLoading && (
          <>
            <div>
              <h2 className="mb-3 text-lg font-semibold">Timeline View</h2>
              <TimelineView timeSlots={displayTimeSlots} />
            </div>

            <div>
              <h2 className="mb-3 text-lg font-semibold">
                Time Slots ({timeSlotSummaryData.length})
              </h2>
              <TimeSlotSummary summaryData={timeSlotSummaryData} />
            </div>

            <Separator />

        
              <div>
                <h2 className="mb-3 text-lg font-semibold">Add time slot</h2>
                <AddTimeSlotForm
                  onAddTimeSlot={handleAddTimeSlot}
                  selectedDate={selectedDate}
                  minStartTime={minStartTimeForNewSlot}
                />
              </div>

            <div>
              <h2 className="mb-3 text-lg font-semibold">
                Time Slots ({displayTimeSlots.length})
              </h2>
              <div className="grid gap-3">
                {displayTimeSlots.length === 0 ? (
                  <p className="text-center text-muted-foreground">
                    No time slots added for this date yet.
                  </p>
                ) : (
                  displayTimeSlots.map((slot) => (
                    <TimeSlotListItem
                      key={slot.id}
                      id={slot.id}
                      start={slot.start}
                      end={slot.end}
                      duration={slot.duration}
                      status={slot.status}
                      onRemove={operatorData ? () => {} : handleRemoveTimeSlot} // Disable remove for API data
                    />
                  ))
                )}
              </div>
            </div>

            <Separator />

            <div>
              <h2 className="mb-3 text-lg font-semibold">Legends</h2>
              <div className="grid grid-cols-2 gap-x-6 gap-y-2 text-sm sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5">
                {legendItems.map(([key, value]) => (
                  <div key={key} className="flex items-center gap-2">
                    <div
                      className={`size-3 shrink-0 rounded-sm ${value.color}`}
                      aria-hidden="true"
                    />
                    <span>
                      <span className="font-medium">{key}</span>:{" "}
                      <span className="text-xs text-muted-foreground">
                        {value.name}
                      </span>
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </>
        )}
      </div>
    </ReusableDialog>
  );
}
