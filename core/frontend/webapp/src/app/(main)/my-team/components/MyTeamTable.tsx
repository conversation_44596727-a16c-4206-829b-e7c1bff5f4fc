"use client";

import type React from "react";
import { useMemo, useState, useRef, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  AttendanceStatus,
  useAttendanceStore,
  ClockingValidationStatus,
  TeamStatus,
} from "../store/myTeamStore";
import { useTeamLeaderStore } from "../store/teamLeaderStore";
import { MyTeamStatusMenue } from "./MyTeamStatusMenue";
import { RequestDialog } from "./dialogs/RequestDialog";
import { RequestFormDialog } from "./dialogs/RequestFormDialog";
import { Checkbox } from "@/components/ui/checkbox";
import { DetailsDialog } from "./dialogs/DetailsDialog";
import { PreviousDayMenu } from "./PreviousDayMenu";
import AttendanceSheetDialog from "./dialogs/AttendanceSheetDialog";
import { UserRole } from "@/enum/rolesEnum";
import { useMockAuthStore } from "@/store/mockAuthStore";
import { resolveUserRole } from "@/utils/userRoleHelper";
import { DatePicker } from "@/components/ui/datePicker";
import CustomSelect from "@/components/common/CustomSelect";
import { ClockingValidationDialog } from "./dialogs/ClockingValidationDialog";
import CustomIcon from "@/components/common/CustomIcons";
import { ChevronLeft, Loader2 } from "lucide-react";
import { SendForReviewDialog } from "./dialogs/SendForReviewDialog";
import { ReplaceDialog } from "./dialogs/ReplaceDialog";
import { Operator } from "../store/replacementStore";
import useReplacementStore from "../store/replacementStore";
import api from "@/lib/axios";

// Helper to safely get string or empty
const safeString = (val: string | undefined) => val || "";

export function MyTeamTable({
  selectedShiftType,
  activeTab,
  selectedWorkers,
  setSelectedWorkers,
  filteredWorkersLength,
}: {
  selectedShiftType?: string;
  activeTab: string;
  selectedWorkers: Set<string>;
  setSelectedWorkers: React.Dispatch<React.SetStateAction<Set<string>>>;
  filteredWorkersLength: number;
}) {
  const { currentUser: user } = useMockAuthStore();
  const {
    workers,
    filterMfgOnly,
    currentDate,
    markAttendance,
    getAttendanceForWorkerAndDate,
    attendanceSheetDetails,
    setAttendanceSheetDetails,
    selectedValidationTeam,
    selectedTeamLeaderInstantId,
    updateTeamStatus,
    isVisualCheckActive,
  } = useAttendanceStore();

  // Team Leader specific store
  const {
    teamData,
    statusMonitoringData,
    // currentStatusData,
    isTeamDataLoading,
    isStatusMonitoringLoading,
    isCurrentStatusLoading,
    // teamDataError,
    // currentStatusError,
    updateOperatorStatus,
    updateOperatorAttendanceStatus,
    statusInfoList,
    teamLeadersStatusData,
    teamOperatorsStatusData,
    fetchTeamOperatorsStatus,
    teamLeaderShiftType,
    changeTeamStatus,
    operatorShiftType,
    operatorsStatusData,
    fetchShiftLeaderStatusMonitoring,
    // DEPARTMENT_CLERK specific
    clerkStatusMonitoringData,
    isClerkStatusMonitoringLoading,
    clerkShiftTypes,
    updateClerkSubordinateStatus,
    // DEPARTMENT_MANAGER specific
    managerStatusMonitoringData,
    isManagerStatusMonitoringLoading,
    managerShiftTypes,
    fetchManagerSubordinateDetails,
    // QUALITY_SUPERVISOR specific
    qualityStatusMonitoringData,
    isQualityStatusMonitoringLoading,
    qualityShiftTypes,
    updateQualitySubordinateStatus,
    updateQualitySubordinateStatusBulk,
    // Local update methods
    updateStatusMonitoringDataLocally,
    updateTeamLeadersStatusDataLocally,
  } = useTeamLeaderStore();

  // Get user role from auth store
  const userRole = resolveUserRole(user);
  const [statusMenuInfo, setStatusMenuInfo] = useState<{
    isOpen: boolean;
    position: { x: number; y: number };
    workerId: string;
    date: string;
    sequenceMode?: boolean;
    replace?: boolean;
    currentStatus?: AttendanceStatus;
    isPreviousDay?: boolean;
  } | null>(null);
  const [isRequestDialogOpen, setIsRequestDialogOpen] = useState(false);
  const [isRequestFormDialogOpen, setIsRequestFormDialogOpen] = useState(false);
  const [selectedRequestType, setSelectedRequestType] = useState<
    | {
        id: string;
        title: string;
      }
    | undefined
  >(undefined);


  const [selectedTeamLeader , setSelectedTeamLeader] = useState<any>(null);
  const [attendanceDialogInfo, setAttendanceDialogInfo] = useState<{
    isOpen: boolean;
    worker: { id: string; name: string; mle: string } | null;
    date: Date | null;
  }>({ isOpen: false, worker: null, date: null });
  const [operatorDetailsDialogInfo, setOperatorDetailsDialogInfo] = useState<{
    isOpen: boolean;
    operatorId: string;
    operatorName: string;
  }>({ isOpen: false, operatorId: "", operatorName: "" });
  const [attendanceSheetDialogInfo, setAttendanceSheetDialogInfo] = useState<{
    isOpen: boolean;
    worker: { id: string; name: string };
    date: Date;
  }>({ isOpen: false, worker: { id: "", name: "" }, date: new Date() });
  const containerRef = useRef<HTMLDivElement>(null);
  const [isClockingValidationDialogOpen, setIsClockingValidationDialogOpen] =
    useState(false);
  const [selectedWorkerId, setSelectedWorkerId] = useState<string | null>(null);
  const [selectedValidationWorkerId, setSelectedValidationWorkerId] = useState<
    string | null
  >(null);
  const [isSendForReviewDialogOpen, setIsSendForReviewDialogOpen] =
    useState(false);

  // State for real-time updates
  const [currentTime, setCurrentTime] = useState(new Date());

  // Update current time every second for real-time counters
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const handleOpenSendForReviewDialog = () => {
    setIsSendForReviewDialogOpen(true);
  };

  // Filter workers based on role, MFG filter, and clocking category
  const filteredWorkers : any[] = useMemo(() => {
    // For Team Leader and Shift Leader roles, use status monitoring data directly


    if (userRole === UserRole.SHIFT_LEADER) {
      

      if (activeTab === "my-teamleaders") {
              // Use reactive store values instead of getState()
              let teamLeadersData = teamLeadersStatusData;
              let shiftType = teamLeaderShiftType;



              console.warn('shiftType' ,shiftType)
              console.warn('teamLeadersStatusData ' , teamLeadersData)

              const teamLeaderStatusToClocking: Record<string, ClockingValidationStatus> = {
                CLOSED: "Waiting validation...",
                REVIEW: "Sent for review",
                VALIDATED: "Validated",
              } as const;


              if (teamLeadersData && teamLeadersData.length > 0) {
              let workers = teamLeadersData.map((operator) => ({
                id: operator.legacy_id.toString(),
                uuid : operator.id,
                mle: operator.legacy_id.toString(),
                firstName: operator.first_name,
                lastName: operator.last_name,
                role: "TEAM LEADER",
                function: 'Team Leader', // Use role as function
                teamleader_instant_id : operator.teamleader_instant_id,
                teamleader_instant_status : operator.teamleader_instant_status,
                shift_type : operator.shift_type,
                start : operator.start,
                end : operator.end,
                dailyRecords : operator.dailyRecords,
                team : operator.teamleader_instant_id,
                clockingValidation :  teamLeaderStatusToClocking[operator.teamleader_instant_status]  || "Not finished yet",
              }));



              console.warn('workers_filtered' ,workers)

              return workers;
        
      }

      }

      else if (activeTab === "my-operators") {

        console.warn('activeTab' ,activeTab)
        // Use reactive store values instead of getState()
        let operatorsData = operatorsStatusData;

        console.warn('teamOperatorsStatusData' ,operatorsData)
        let shiftType = operatorShiftType;
        console.warn('shiftType_operator' ,shiftType)
        if (operatorsStatusData && operatorsStatusData.length > 0) {
          let workers = operatorsStatusData.map((operator) => ({
            id: operator.legacy_id.toString(),
            uuid : operator.id,
            mle: operator.legacy_id.toString(),
            firstName: operator.first_name,
            lastName: operator.last_name,
            shift_type : operator.shift_type,
            line: undefined,
            status: undefined,
            dailyrecords : operator.dailyRecords,
            clockingValidation: "Not finished yet", // Default value
          }));
          workers = workers.filter((worker) => worker.shift_type === shiftType);
          return workers;
        }
        return [];
      }



    }
    if (userRole === UserRole.TEAM_LEADER ) {
      
      const selectedTeam =
        useAttendanceStore.getState().teamLeaderFilters.selectedTeam;


      console.warn('selectedTeam' ,selectedTeam)
      // Get operators directly from status monitoring data
      const statusOperators = statusMonitoringData?.status_data || [];



      console.log("statusOperators", statusOperators);
      // Convert status monitoring operators to worker format
      // Determine clockingValidation mapping from teamLeadersStatusData
      const teamLeaderStatusToClocking: Record<string, ClockingValidationStatus> = {
        CLOSED: "Waiting validation...",
        REVIEW: "Sent for review",
        VALIDATED: "Validated",
      } as const;

      // Choose team leader instant dynamically: prefer user-selected instant id, else NORMAL, else first
      const selectedTlInstant =
        (teamLeadersStatusData || []).find(
          (t) => t.teamleader_instant_id === selectedTeamLeaderInstantId,
        ) ||
        (teamLeadersStatusData || []).find((t) => t.shift_type === "NORMAL") ||
        (teamLeadersStatusData && teamLeadersStatusData[0]);
      const mappedClockingValidation: ClockingValidationStatus = selectedTlInstant
        ? teamLeaderStatusToClocking[selectedTlInstant.teamleader_instant_status] || "Not finished yet"
        : "Not finished yet";

      let workers = statusOperators.map((operator) => ({
        id: operator.legacy_id.toString(),
        uuid : operator.id,
        mle: operator.legacy_id.toString(),
        firstName: operator.first_name,
        lastName: operator.last_name,
        role: operator.role,
        function: operator.role, // Use role as function
        team: operator.team_id,
        line: undefined,
        status: undefined, // Will be determined by getCurrentStatusForOperator
        clockingValidation: mappedClockingValidation,
      }));

      // Filter by selected team if one is chosen
      if (selectedTeam) {
        workers = workers.filter((worker) => worker.team === selectedTeam);
      }
 
      // // Filter by MFG if enabled
      // if (filterMfgOnly) {
      //   workers = workers.filter(
      //     (worker) => worker.role.toLowerCase() === "mfg structure",
      //   );
      // }

      return workers;
    }

    // For DEPARTMENT_CLERK role, use clerk status monitoring data
    if (userRole === UserRole.DEPARTEMENT_CLERK && clerkStatusMonitoringData) {
      const clerkWorkers = clerkStatusMonitoringData.status_data.map((subordinate) => ({
        id: subordinate?.legacy_id?.toString(),
        uuid: subordinate.id,
        mle: subordinate?.legacy_id?.toString(),
        firstName: subordinate.first_name,
        lastName: subordinate.last_name,
        role: subordinate?.role,
        function: subordinate?.function,
        team: subordinate.team_id,
        shift_type: subordinate.shift_type,
        dailyRecords: subordinate.dailyRecords,
        category: subordinate?.category,
        clockingValidation: "Not finished yet",
      }));

      let filtered = clerkWorkers;
      if (activeTab === "clocking-ih") {
        filtered = filtered.filter((worker) => worker.category === "IH");
      } else if (activeTab === "clocking-is") {
        filtered = filtered.filter((worker) => worker.category === "IS");
      }

      return filtered;
    }

    // For DEPARTMENT_MANAGER role, use manager status monitoring data
    if (userRole === UserRole.DEPARTEMENT_MANAGER && managerStatusMonitoringData) {
      const managerWorkers = managerStatusMonitoringData.status_data.map((subordinate) => ({
        id: subordinate?.legacy_id?.toString(),
        uuid: subordinate.id,
        mle: subordinate?.legacy_id?.toString(),
        firstName: subordinate.first_name,
        lastName: subordinate.last_name,
        role: subordinate?.role,
        function: subordinate?.function,
        team: subordinate.team_id,
        shift_type: subordinate.shift_type,
        dailyRecords: subordinate.dailyRecords,
        category: subordinate?.category,
        clockingValidation: "Not finished yet",
      }));

      let filtered = managerWorkers;
      if (activeTab === "clocking-ih") {
        filtered = filtered.filter((worker) => worker.category === "IH");
      } else if (activeTab === "clocking-is") {
        filtered = filtered.filter((worker) => worker.category === "IS");
      }

      return filtered;
    }

    // QUALITY_SUPERVISOR filtering logic
    if (userRole === UserRole.QUALITY_SUPERVISOR && qualityStatusMonitoringData?.status_data) {
      const qualityWorkers = qualityStatusMonitoringData.status_data.map((subordinate) => ({
        id: subordinate?.id,
        uuid: subordinate.id,
        mle: subordinate?.legacy_id?.toString(),
        firstName: subordinate.first_name,
        lastName: subordinate.last_name,
        role: subordinate?.role,
        function: subordinate?.function,
        team: subordinate.team_id,
        isMFG: subordinate.isMFG,
        dailyRecords: subordinate.dailyRecords,
        clockingValidation: "Not finished yet",
      }));

      let filtered = qualityWorkers;
      // Apply MFG filter if needed
      if (filterMfgOnly) {
        filtered = filtered.filter((worker) => worker.isMFG === true);
      }

      return filtered;
    }

    // For other roles, use existing workers (they have their own data sources)
    let filtered = workers;

    // // Filter by MFG if enabled (for Team Leader)
    // if (filterMfgOnly) {
    //   filtered = filtered.filter(
    //     (worker) => worker.role.toLowerCase() === "mfg structure",
    //   );
    // }

    // Filter by clocking category for Department Clerk and Department Manager
    if (
      userRole === UserRole.DEPARTEMENT_CLERK ||
      userRole === UserRole.DEPARTEMENT_MANAGER
    ) {
      if (activeTab === "clocking-ih") {
        filtered = filtered.filter((worker) => worker.category === "IH");
      } else if (activeTab === "clocking-is") {
        filtered = filtered.filter((worker) => worker.category === "IS");
      }
    }

    return filtered;
  }, [workers, filterMfgOnly, userRole, activeTab, statusMonitoringData , teamLeadersStatusData , teamLeaderShiftType , operatorShiftType , operatorsStatusData, clerkStatusMonitoringData, managerStatusMonitoringData, qualityStatusMonitoringData]);

  // Generate calendar days - for Team/Shift Leader/Department Clerk use API date range, for others use current month
  const calendarDays = useMemo(() => {
    // For Team Leader and Shift Leader, use status monitoring data
    if ((userRole === UserRole.TEAM_LEADER || userRole === UserRole.SHIFT_LEADER) && statusMonitoringData?.status_data && statusMonitoringData.status_data.length > 0) {
      // Get unique dates from the status monitoring data
      const allDates = new Set<string>();
      statusMonitoringData.status_data.forEach(operator => {
        operator.dailyRecords.forEach(record => {
          allDates.add(record.date);
        });
      });

      // Add today and next 3 days if not already present
      const today = new Date();
      for (let i = 0; i < 4; i++) {
        const date = new Date(today);
        date.setDate(today.getDate() + i);
        const dateStr = date.toISOString().split("T")[0];
        allDates.add(dateStr);
      }

      const sortedDates = Array.from(allDates).sort();

      return sortedDates.map(dateStr => {
        const date = new Date(dateStr + 'T00:00:00');


        console.log('is_today',date.toDateString() === new Date().toDateString())

        return {
          day: date.getDate(),
          date: dateStr,
          dayName: date.toLocaleDateString("en-US", { weekday: "short" }),
          isToday: date.toDateString() === new Date().toDateString(),
          isFutureStatic: date > new Date() && date <= new Date(Date.now() + 3 * 24 * 60 * 60 * 1000), // Next 3 days
        };
      });
    }

    // For Department Clerk, use clerk status monitoring data
    if (userRole === UserRole.DEPARTEMENT_CLERK && clerkStatusMonitoringData?.status_data && clerkStatusMonitoringData.status_data.length > 0) {
      // Get unique dates from the clerk status monitoring data
      const allDates = new Set<string>();
      clerkStatusMonitoringData.status_data.forEach(subordinate => {
        subordinate.dailyRecords.forEach(record => {
          allDates.add(record.date);
        });
      });

      // Add today and next 3 days if not already present
      const today = new Date();
      for (let i = 0; i < 4; i++) {
        const date = new Date(today);
        date.setDate(today.getDate() + i);
        const dateStr = date.toISOString().split("T")[0];
        allDates.add(dateStr);
      }

      const sortedDates = Array.from(allDates).sort();
      return sortedDates.map(dateStr => {
        const date = new Date(dateStr + 'T00:00:00');
        return {
          day: date.getDate(),
          date: dateStr,
          dayName: date.toLocaleDateString("en-US", { weekday: "short" }),
          isToday: date.toDateString() === new Date().toDateString(),
          isFutureStatic: date > new Date() && date <= new Date(Date.now() + 3 * 24 * 60 * 60 * 1000), // Next 3 days
        };
      });
    }

    // For Department Manager, use manager status monitoring data
    if (userRole === UserRole.DEPARTEMENT_MANAGER && managerStatusMonitoringData?.status_data && managerStatusMonitoringData.status_data.length > 0) {
      // Get unique dates from the manager status monitoring data
      const allDates = new Set<string>();
      managerStatusMonitoringData.status_data.forEach(subordinate => {
        subordinate.dailyRecords.forEach(record => {
          allDates.add(record.date);
        });
      });

      const sortedDates = Array.from(allDates).sort();

      return sortedDates.map(dateStr => {
        const date = new Date(dateStr + 'T00:00:00');
        return {
          day: date.getDate(),
          date: dateStr,
          dayName: date.toLocaleDateString("en-US", { weekday: "short" }),
          isToday: date.toDateString() === new Date().toDateString(),
          isFutureStatic: date > new Date() && date <= new Date(Date.now() + 3 * 24 * 60 * 60 * 1000), // Next 3 days
        };
      });
    }

    // For Quality Supervisor, use quality status monitoring data
    if (userRole === UserRole.QUALITY_SUPERVISOR && qualityStatusMonitoringData?.status_data && qualityStatusMonitoringData.status_data.length > 0) {
      // Get unique dates from the quality status monitoring data
      const allDates = new Set<string>();
      qualityStatusMonitoringData.status_data.forEach(subordinate => {
        subordinate.dailyRecords.forEach(record => {
          allDates.add(record.date);
        });
      });

      const sortedDates = Array.from(allDates).sort();

      return sortedDates.map(dateStr => {
        const date = new Date(dateStr + 'T00:00:00');
        return {
          day: date.getDate(),
          date: dateStr,
          dayName: date.toLocaleDateString("en-US", { weekday: "short" }),
          isToday: date.toDateString() === new Date().toDateString(),
          isFutureStatic: date > new Date() && date <= new Date(Date.now() + 3 * 24 * 60 * 60 * 1000), // Next 3 days
        };
      });
    }

    // For other roles, use current month as before
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();
    const daysInMonth = new Date(year, month + 1, 0).getDate();
    return Array.from({ length: daysInMonth }, (_, i) => {
      const day = i + 1;
      const date = new Date(year, month, day);
      return {
        day,
        date: date.toISOString().split("T")[0],
        dayName: date.toLocaleDateString("en-US", { weekday: "short" }),
        isToday: date.toDateString() === new Date().toDateString(),
        isFutureStatic: false,
      };
    });
  }, [userRole, statusMonitoringData, currentDate, clerkStatusMonitoringData, managerStatusMonitoringData]);

  const getStatusColor = (status: AttendanceStatus) => {
    switch (status) {
      case "P":
        return "bg-[#4CAF501C] text-[#4CAF50] hover:bg-green-100 hover:text-[#4CAF50]";
      case "P_IN_BUS":
        return "bg-[#fff4c8] text-[#c59800] hover:bg-blue-100 hover:text-[#1976D2]";
      case "P_IN_PLANT":
        return "bg-[#E8F5E8] text-[#2E7D32] hover:bg-green-100 hover:text-[#2E7D32]";
      case "CTN":
        return "bg-[#FBE3E3] text-[#D60000] hover:bg-red-200 hover:text-[#D60000]";
      case "MA":
        return "bg-[#FBE3E3] text-[#D60000] hover:bg-red-200 hover:text-[#D60000]";
      case "AB":
        return "bg-[#FBE3E3] text-[#D60000] hover:bg-red-200 hover:text-[#D60000]";
      default:
        return "bg-white text-black";
    }
  };

  // Helper function to format time from ISO string
  const formatTime = (isoString: string) => {
    const date = new Date(isoString);
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    });
  };

  const getStatusForWorkerAndDate = (workerId: string, date: string): AttendanceStatus => {
    // For DEPARTMENT_CLERK, use clerk status monitoring data
    if (userRole === UserRole.DEPARTEMENT_CLERK && clerkStatusMonitoringData) {
      const subordinate = clerkStatusMonitoringData.status_data.find(
        (sub) => sub.legacy_id.toString() === workerId
      );

      if (subordinate) {
        const dailyRecord = subordinate.dailyRecords.find(record => record.date === date);
        if (dailyRecord && dailyRecord.statusCodes.length > 0) {
          const latestStatus = dailyRecord.statusCodes[dailyRecord.statusCodes.length - 1];
          return latestStatus.code as AttendanceStatus;
        }
      }
      return "";
    }

    if (userRole === UserRole.DEPARTEMENT_MANAGER && managerStatusMonitoringData) {
      const subordinate = managerStatusMonitoringData.status_data.find(
        (sub) => sub.legacy_id.toString() === workerId
      );

      if (subordinate) {
        const dailyRecord = subordinate.dailyRecords.find(record => record.date === date);
        if (dailyRecord && dailyRecord.statusCodes.length > 0) {
          const latestStatus = dailyRecord.statusCodes[dailyRecord.statusCodes.length - 1];
          return latestStatus.code as AttendanceStatus;
        }
      }
      return "";
    }

    // For QUALITY_SUPERVISOR, use quality status monitoring data
    if (userRole === UserRole.QUALITY_SUPERVISOR && qualityStatusMonitoringData) {
      const subordinate = qualityStatusMonitoringData.status_data.find(
        (sub) => sub.id === workerId
      );

      if (subordinate) {
        const dailyRecord = subordinate.dailyRecords.find(record => record.date === date);
        if (dailyRecord && dailyRecord.statusCodes.length > 0) {
          const latestStatus = dailyRecord.statusCodes[dailyRecord.statusCodes.length - 1];
          return latestStatus.code as AttendanceStatus;
        }
      }
      return "";
    }

    // For TEAM_LEADER, use status monitoring data
    if (userRole === UserRole.TEAM_LEADER && statusMonitoringData) {
      const operator = statusMonitoringData.status_data.find(
        (op) => op.legacy_id.toString() === workerId
      );

      if (operator) {
        const dailyRecord = operator.dailyRecords.find(record => record.date === date);
        if (dailyRecord && dailyRecord.statusCodes.length > 0) {
          const latestStatus = dailyRecord.statusCodes[dailyRecord.statusCodes.length - 1];
          return latestStatus.code as AttendanceStatus;
        }
      }
      return "";
    }

    // For other roles, use the existing attendance store method
    return getAttendanceForWorkerAndDate(workerId, date);
  };

  // Helper function for shift status
  const getShiftStatus = () => {
    return statusMonitoringData?.shift_status || "INIT";
  };

  // Helper function to get status color from API data
  const getStatusColorFromAPI = (statusCode: string) => {
    const statusInfo = statusInfoList.find(info => info.statusCode === statusCode);
    if (statusInfo) {
      return `bg-[${statusInfo.color}] text-white`;
    }
    // Fallback to existing color logic
    return getStatusColor(statusCode as AttendanceStatus);
  };

  const getClockingValidationColor = (status: ClockingValidationStatus) => {
    switch (status) {
      case "Validated":
        return "text-green-600";
      case "Waiting validation...":
        return "text-orange-600 cursor-pointer hover:text-orange-700";
      case "Absent":
        return "text-red-600 cursor-pointer hover:text-red-700";
      case "Not finished yet":
        return "text-gray-500";
      default:
        return "text-gray-500";
    }
  };

  const handleCellClick = (
    workerId: string,
    date: string,
    isToday: boolean,
    event: React.MouseEvent,
  ) => {




    console.log('cell_clicked....')

    if (!isToday && (userRole === UserRole.TEAM_LEADER) && statusMonitoringData?.status_data) {
      const operator = statusMonitoringData.status_data.find(
        (op) => op.legacy_id.toString() === workerId
      );

      if (operator) {
        const dailyRecord = operator.dailyRecords.find(record => record.date === date);
        if (dailyRecord && dailyRecord.statusCodes.length > 0) {
          // Get the last status for this date
          const lastStatus = dailyRecord.statusCodes[dailyRecord.statusCodes.length - 1];
          const shiftStatus = getShiftStatus();

          // Open the status menu for previous days
          const cellRect = (
            event.currentTarget as HTMLElement
          ).getBoundingClientRect();
          const containerRect = containerRef.current?.getBoundingClientRect();
          let x = cellRect.left;
          let y = cellRect.bottom;
          if (containerRect && containerRef.current) {
            const MENU_OFFSET_X = 0;
            const MENU_OFFSET_Y = 0;
            x =
              cellRect.left -
              containerRect.left +
              containerRef.current.scrollLeft +
              cellRect.width +
              MENU_OFFSET_X;
            y =
              cellRect.bottom -
              containerRect.top +
              containerRef.current.scrollTop +
              MENU_OFFSET_Y;
          }

          // If shift status is REVIEW, show full dropdown instead of previous day menu
          if (shiftStatus === "REVIEW") {
            setStatusMenuInfo({
              isOpen: true,
              position: { x, y },
              workerId,
              date,
              replace: false,
              currentStatus: lastStatus.code as AttendanceStatus,
              isPreviousDay: false, // Show full dropdown
            });
          } else {
            setStatusMenuInfo({
              isOpen: true,
              position: { x, y },
              workerId,
              date,
              replace: false, // No replace option for previous days
              currentStatus: lastStatus.code as AttendanceStatus,
              isPreviousDay: true, // Add flag to indicate this is a previous day
            });
          }
          return;
        }
      }
    }

    // Allow team leaders to see dropdown for old dates when shift status is REVIEW
    const shiftStatus = getShiftStatus();
    const canShowOldDateDropdown = userRole === UserRole.TEAM_LEADER && shiftStatus === "REVIEW";

    if (!isToday && !canShowOldDateDropdown) return;

    // Open menu for Shift Leader to manage Team Leaders (use teamLeadersStatusData on my-teamleaders)
    if (userRole === UserRole.SHIFT_LEADER) {
      const tlList = activeTab === "my-teamleaders" ? (teamLeadersStatusData || []) : (statusMonitoringData?.status_data || []);
      const operator = tlList.find(
        (op: any) => op.legacy_id.toString() === workerId
      );

      if (operator) {

        console.log('found operator' ,operator)
        const dailyRecord = (operator.dailyRecords || []).find((record: any) => record.date === date);
        let currentStatus: AttendanceStatus = "";
        if (dailyRecord && dailyRecord.statusCodes.length > 0) {
          const latestStatus = dailyRecord.statusCodes[dailyRecord.statusCodes.length - 1];
          currentStatus = latestStatus.code as AttendanceStatus;
        }

        const cellRect = (
          event.currentTarget as HTMLElement
        ).getBoundingClientRect();
        const containerRect = containerRef.current?.getBoundingClientRect();
        let x = cellRect.left;
        let y = cellRect.bottom;
        if (containerRect && containerRef.current) {
          const MENU_OFFSET_X = 0;
          const MENU_OFFSET_Y = 0;
          x =
            cellRect.left -
            containerRect.left +
            containerRef.current.scrollLeft +
            cellRect.width +
            MENU_OFFSET_X;
          y =
            cellRect.bottom -
            containerRect.top +
            containerRef.current.scrollTop +
            MENU_OFFSET_Y;
        }
        setStatusMenuInfo({
          isOpen: true,
          position: { x, y },
          workerId,
          date,
          replace: false,
          currentStatus,
        });
        return;
      }
    }
    if ((userRole === UserRole.TEAM_LEADER) && statusMonitoringData?.status_data) {
      const operator = statusMonitoringData.status_data.find(
        (op) => op.legacy_id.toString() === workerId
      );

      if (operator) {
        // Find the daily record for this specific date
        const dailyRecord = operator.dailyRecords.find(record => record.date === date);
        let currentStatus: AttendanceStatus = "";

        if (dailyRecord && dailyRecord.statusCodes.length > 0) {
          // Get the latest status code for this date
          const latestStatus = dailyRecord.statusCodes[dailyRecord.statusCodes.length - 1];
          currentStatus = latestStatus.code as AttendanceStatus;
        }

        const shiftStatus = getShiftStatus();

        // Allow interaction if it's today OR if shift status is REVIEW (for old dates)
        const canInteract = isToday || shiftStatus === "REVIEW";

        if (canInteract && (currentStatus === "P" || currentStatus === "P_IN_BUS" || currentStatus === "P_IN_PLANT" || currentStatus === "AB")) {
          // Open the status menu
          const cellRect = (
            event.currentTarget as HTMLElement
          ).getBoundingClientRect();
          const containerRect = containerRef.current?.getBoundingClientRect();
          let x = cellRect.left;
          let y = cellRect.bottom;
          if (containerRect && containerRef.current) {
            const MENU_OFFSET_X = 0;
            const MENU_OFFSET_Y = 0;
            x =
              cellRect.left -
              containerRect.left +
              containerRef.current.scrollLeft +
              cellRect.width +
              MENU_OFFSET_X;
            y =
              cellRect.bottom -
              containerRect.top +
              containerRef.current.scrollTop +
              MENU_OFFSET_Y;
          }
          setStatusMenuInfo({
            isOpen: true,
            position: { x, y },
            workerId,
            date,
            replace: currentStatus === "AB",
            currentStatus,
          });
          return;
        }

        // If no status and we're in VISUAL_CHECK mode OR REVIEW mode, show attendance options
        if (canInteract && (shiftStatus === "VISUAL_CHECK" && currentStatus === "" || statusMonitoringData?.shift_status == 'REVIEW')) {
          // Open the status menu for attendance marking
          const cellRect = (
            event.currentTarget as HTMLElement
          ).getBoundingClientRect();
          const containerRect = containerRef.current?.getBoundingClientRect();
          let x = cellRect.left;
          let y = cellRect.bottom;
          if (containerRect && containerRef.current) {
            const MENU_OFFSET_X = 0;
            const MENU_OFFSET_Y = 0;
            x =
              cellRect.left -
              containerRect.left +
              containerRef.current.scrollLeft +
              cellRect.width +
              MENU_OFFSET_X;
            y =
              cellRect.bottom -
              containerRect.top +
              containerRef.current.scrollTop +
              MENU_OFFSET_Y;
          }
          setStatusMenuInfo({
            isOpen: true,
            position: { x, y },
            workerId,
            date,
            replace: false,
            currentStatus,
          });
          return;
        }

        // If not P or AB, update to P (for other statuses)
        const shiftInstantId = statusMonitoringData?.shift_instant_id || teamData?.shiftInstantId;
        if (shiftInstantId && currentStatus !== "") {
          updateOperatorStatus(shiftInstantId, operator.uuid, "P");
        }
        return;
      }
    }

    // DEPARTMENT_CLERK: handle subordinate status updates
    if (userRole === UserRole.DEPARTEMENT_CLERK && clerkStatusMonitoringData?.status_data) {

      console.log('department_clerk_clicked' , workerId)
      const subordinate = clerkStatusMonitoringData.status_data.find(
        (sub) => sub.legacy_id.toString() === workerId
      );


      console.log('subordinate' ,  subordinate)

      if (subordinate) {
        // Find the daily record for this specific date
        const dailyRecord = subordinate.dailyRecords.find(record => record.date === date);
        let currentStatus: AttendanceStatus = "";

        if (dailyRecord && dailyRecord.statusCodes.length > 0) {
          // Get the latest status code for this date
          const latestStatus = dailyRecord.statusCodes[dailyRecord.statusCodes.length - 1];
          currentStatus = latestStatus.code as AttendanceStatus;
        }

        // Open the status menu for department clerk
        const cellRect = (
          event.currentTarget as HTMLElement
        ).getBoundingClientRect();
        const containerRect = containerRef.current?.getBoundingClientRect();
        let x = cellRect.left;
        let y = cellRect.bottom;
        if (containerRect && containerRef.current) {
          const MENU_OFFSET_X = 0;
          const MENU_OFFSET_Y = 0;
          x =
            cellRect.left -
            containerRect.left +
            containerRef.current.scrollLeft +
            cellRect.width +
            MENU_OFFSET_X;
          y =
            cellRect.bottom -
            containerRect.top +
            containerRef.current.scrollTop +
            MENU_OFFSET_Y;
        }
        setStatusMenuInfo({
          isOpen: true,
          position: { x, y },
          workerId,
          date,
          replace: false,
          currentStatus,
        });
        return;
      }
    }

    // DEPARTMENT_MANAGER: handle subordinate status updates
    if (userRole === UserRole.DEPARTEMENT_MANAGER && managerStatusMonitoringData?.status_data) {
      console.log('department_manager_clicked', workerId);
      const subordinate = managerStatusMonitoringData.status_data.find(
        (sub) => sub.legacy_id.toString() === workerId
      );

      console.log('manager_subordinate', subordinate);

      if (subordinate) {
        // Find the daily record for this specific date
        const dailyRecord = subordinate.dailyRecords.find(record => record.date === date);
        let currentStatus: AttendanceStatus = "";

        if (dailyRecord && dailyRecord.statusCodes.length > 0) {
          // Get the latest status code for this date
          const latestStatus = dailyRecord.statusCodes[dailyRecord.statusCodes.length - 1];
          currentStatus = latestStatus.code as AttendanceStatus;
        }

        // For DEPARTMENT_MANAGER, show details dialog instead of status menu
        // Open details dialog with subordinate information
        fetchManagerSubordinateDetails(
          managerShiftTypes[0]?.clerk_instant_id || '',
          subordinate.id
        ).then(detailsData => {
          // You can open a details dialog here or show more information
          console.log('Manager subordinate details:', detailsData);
        }).catch(error => {
          console.error('Failed to fetch subordinate details:', error);
        });
        return;
      }
    }

    // For QUALITY_SUPERVISOR, handle subordinate status updates
    if (userRole === UserRole.QUALITY_SUPERVISOR && qualityStatusMonitoringData?.status_data) {
      console.log('quality_supervisor_clicked', workerId);
      const subordinate = qualityStatusMonitoringData.status_data.find(
        (sub) => sub.id === workerId
      );

      console.log('quality_subordinate', subordinate);

      if (subordinate) {
        // Find the daily record for this specific date
        const dailyRecord = subordinate.dailyRecords.find(record => record.date === date);
        let currentStatus: AttendanceStatus = "";

        if (dailyRecord && dailyRecord.statusCodes.length > 0) {
          // Get the latest status code for this date
          const latestStatus = dailyRecord.statusCodes[dailyRecord.statusCodes.length - 1];
          currentStatus = latestStatus.code as AttendanceStatus;
        }

        // Open status menu for quality supervisor
        const cellRect = (
          event.currentTarget as HTMLElement
        ).getBoundingClientRect();
        const containerRect = containerRef.current?.getBoundingClientRect();
        let x = cellRect.left;
        let y = cellRect.bottom;
        if (containerRect && containerRef.current) {
          const MENU_OFFSET_X = 0;
          const MENU_OFFSET_Y = 0;
          x =
            cellRect.left -
            containerRect.left +
            containerRef.current.scrollLeft +
            MENU_OFFSET_X;
          y =
            cellRect.bottom -
            containerRect.top +
            containerRef.current.scrollTop +
            MENU_OFFSET_Y;
        }

        setStatusMenuInfo({
          workerId,
          date,
          currentStatus,
          position: { x, y },
          isOpen: true,
        });
        return;
      }
    }

    // For other roles, use existing logic
    const currentStatus = getAttendanceForWorkerAndDate(workerId, date);
    const isSequence =
      typeof currentStatus === "string" && currentStatus.includes(",");

    if (currentStatus === "P" || currentStatus === "AB" || isSequence) {
      // Open the status menu
      const cellRect = (
        event.currentTarget as HTMLElement
      ).getBoundingClientRect();
      const containerRect = containerRef.current?.getBoundingClientRect();
      let x = cellRect.left;
      let y = cellRect.bottom;
      if (containerRect && containerRef.current) {
        const MENU_OFFSET_X = 0;
        const MENU_OFFSET_Y = 0;
        x =
          cellRect.left -
          containerRect.left +
          containerRef.current.scrollLeft +
          cellRect.width +
          MENU_OFFSET_X;
        y =
          cellRect.bottom -
          containerRect.top +
          containerRef.current.scrollTop +
          MENU_OFFSET_Y;
      }
      setStatusMenuInfo({
        isOpen: true,
        position: { x, y },
        workerId,
        date,
        replace: currentStatus === "AB",
        currentStatus,
      });
      return;
    }

    // If not P or sequence, always revert to P on click
    // markAttendance(workerId, date, "P");
  };

  const handleStatusSelect = (status: AttendanceStatus) => {
    if (statusMenuInfo) {
      console.log("Setting sequence mode for status:", status);

      if (userRole === UserRole.SHIFT_LEADER) {
        const shiftInstantId = statusMonitoringData?.shift_instant_id || teamData?.shiftInstantId;
        if (activeTab === "my-teamleaders") {
          const tl = (teamLeadersStatusData || []).find(
            (t: any) => t.legacy_id?.toString() === statusMenuInfo.workerId
          );


          console.log('tl__________' , tl , 'shiftInstantId' , shiftInstantId)
          if (tl && shiftInstantId) {


            (async () => {
              try {
                await api.post(
                  `visual-check/api/v1/shiftleader/shift_instant/${shiftInstantId}/teamleader/${tl.id}/status`,
                  {
                    statusCode: status,
                    shiftType: (teamLeaderShiftType || "NORMAL").toUpperCase(),
                    subShiftInstantId: tl.teamleader_instant_id || "",
                  },
                );

             

                updateTeamLeadersStatusDataLocally(tl.legacy_id, status);

                // Refresh the data after successful update
                const endDate = new Date().toISOString().split("T")[0];
                await fetchShiftLeaderStatusMonitoring(endDate);
                

              } catch (e) {
                console.error("Failed to update team leader status:", e);
              } finally {
                closeStatusMenu();
              }
            })();
            return;
          }
        }

        if (activeTab === "my-operators") {
          const opList: any[] = (operatorsStatusData || statusMonitoringData?.status_data || []) as any[];
          const op = opList.find((o: any) => o.legacy_id?.toString() === statusMenuInfo.workerId);
          if (op && shiftInstantId) {
            (async () => {
              try {
                await api.post(
                  `visual-check/api/v1/shiftleader/shift_instant/${shiftInstantId}/operators/${op.id}/status`,
                  {
                    statusCode: status,
                    shiftType: (operatorShiftType || "NORMAL").toUpperCase(),
                  },
                );

                // Update the operator's daily records locally for immediate UI feedback
                updateStatusMonitoringDataLocally(op.id, status);

                // Refresh the data after successful update
                const endDate = new Date().toISOString().split("T")[0];
                await fetchShiftLeaderStatusMonitoring(endDate);
              } catch (e) {
                console.error("Failed to update operator status:", e);
              } finally {
                closeStatusMenu();
              }
            })();
            return;
          }
        }

      }
      // TEAM LEADER: update operator statuses
      if (userRole === UserRole.TEAM_LEADER && statusMonitoringData?.status_data) {
        const operator = statusMonitoringData.status_data.find(
          (op) => op.legacy_id.toString() === statusMenuInfo.workerId,
        );
        const shiftInstantId = statusMonitoringData?.shift_instant_id || teamData?.shiftInstantId;
        const shiftStatus = getShiftStatus();
        if (operator && shiftInstantId) {
          // // On my-operators tab, call backend API using operatorShiftType
          // if (activeTab === "my-operators") {
          //   (async () => {
          //     try {
          //       await api.post(
          //         `visual-check/api/v1/shiftleader/shift_instant/${shiftInstantId}/operators/${operator.id}/status`,
          //         {
          //           statusCode: status,
          //           shiftType: (operatorShiftType || "NORMAL").toUpperCase(),
          //         },
          //       );

          //       // Update the operator's daily records locally for immediate UI feedback
          //       updateStatusMonitoringDataLocally(operator.id, status);
          //     } catch (e) {
          //       console.error("Failed to update operator status (Team Leader):", e);
          //     } finally {
          //       closeStatusMenu();
          //     }
          //   })();
          //   return;
          // }
          if (shiftStatus === "VISUAL_CHECK" && (status === "P" || status === "AB")) {
            updateOperatorAttendanceStatus(shiftInstantId, operator.id, status);
          } else {
            updateOperatorStatus(shiftInstantId, operator.uuid, status);
          }
          closeStatusMenu();
          return;
        }
      }

      // DEPARTMENT_CLERK: update subordinate status
      if (userRole === UserRole.DEPARTEMENT_CLERK && clerkStatusMonitoringData?.status_data) {
        const subordinate = clerkStatusMonitoringData.status_data.find(
          (sub) => sub.legacy_id.toString() === statusMenuInfo.workerId,
        );

        if (subordinate && clerkShiftTypes.length > 0) {
          // Get the clerk instant ID from the first shift type (assuming single clerk instant)
          const clerkInstantId = clerkShiftTypes[0]?.clerk_instant_id;

          if (clerkInstantId) {
            updateClerkSubordinateStatus(clerkInstantId, subordinate.id, status);
            closeStatusMenu();
            return;
          }
        }
      }

      // QUALITY_SUPERVISOR: update subordinate status
      if (userRole === UserRole.QUALITY_SUPERVISOR && qualityStatusMonitoringData?.status_data) {
        const subordinate = qualityStatusMonitoringData.status_data.find(
          (sub) => sub.id === statusMenuInfo.workerId,
        );

        if (subordinate && qualityShiftTypes.length > 0) {
          // Get the quality instant ID from the first shift type
          const qualityInstantId = qualityShiftTypes[0]?.shift_instant_id;

          if (qualityInstantId) {
            updateQualitySubordinateStatus(qualityInstantId, subordinate.id, status);
            closeStatusMenu();
            return;
          }
        }
      }

      // For other roles, use existing logic
      if (statusMenuInfo.sequenceMode) {
        // Set all hours (6-13) to the selected status as a sequence (comma-separated)
        const details = Array(8).fill(status); // 8 hours: 6-13
        setAttendanceSheetDetails(
          statusMenuInfo.workerId,
          statusMenuInfo.date,
          details,
        );
        closeStatusMenu();
        return;
      }
      // For single status, always set as a plain string (not a sequence)
      markAttendance(statusMenuInfo.workerId, statusMenuInfo.date, status);
      // Clear any sequence for this cell/date
      setAttendanceSheetDetails(
        statusMenuInfo.workerId,
        statusMenuInfo.date,
        [],
      );
      closeStatusMenu();
    }
  };

  const closeStatusMenu = () => {
    setStatusMenuInfo(null);
  };

  // Helper function to check if any data is loading for the current user role
  const isAnyDataLoading = () => {
    switch (userRole) {
      case UserRole.DEPARTEMENT_CLERK:
        return isClerkStatusMonitoringLoading;
      case UserRole.DEPARTEMENT_MANAGER:
        return isManagerStatusMonitoringLoading;
      case UserRole.QUALITY_SUPERVISOR:
        return isQualityStatusMonitoringLoading;
      case UserRole.TEAM_LEADER:
        return isTeamDataLoading || isStatusMonitoringLoading || isCurrentStatusLoading;
      case UserRole.SHIFT_LEADER:
        return isStatusMonitoringLoading;
      default:
        return false;
    }
  };

  const handleOpenRequestDialog = () => {
    setIsRequestDialogOpen(true);
    closeStatusMenu();
  };

  const handleCloseRequestDialog = () => {
    setIsRequestDialogOpen(false);
  };

  const handleOpenRequestForm = (requestType: {
    id: string;
    title: string;
  }) => {
    setSelectedRequestType(requestType);
    setIsRequestFormDialogOpen(true);
    setIsRequestDialogOpen(false);
  };

  const handleCloseRequestFormDialog = () => {
    setIsRequestFormDialogOpen(false);
    setSelectedRequestType(undefined);
  };

  const handleBackToRequestList = () => {
    setIsRequestFormDialogOpen(false);
    setIsRequestDialogOpen(true);
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedWorkers(new Set(filteredWorkers.map((w) => w.uuid)));
    } else {
      setSelectedWorkers(new Set());
    }
  };

  const handleSelectWorker = (workerId: string, checked: boolean) => {

    console.log('worker_id' , workerId)
    const newSelected = new Set(selectedWorkers);
    if (checked) {
      newSelected.add(workerId);
    } else {
      newSelected.delete(workerId);
    }
    setSelectedWorkers(newSelected);
  };

  const handleOpenAttendanceSheetDialog = () => {
    if (statusMenuInfo) {
      if (userRole === UserRole.TEAM_LEADER && statusMonitoringData?.status_data) {
        const operator = statusMonitoringData.status_data.find(
          (op) => op.legacy_id.toString() === statusMenuInfo.workerId,
        );
        if (operator) {
          setOperatorDetailsDialogInfo({
            isOpen: true,
            operatorId: operator.id, 
            operatorName: `${operator.first_name} ${operator.last_name}`,
          });
        }
      } else {
        const worker = workers.find((w) => w.id === statusMenuInfo.workerId);
        if (worker) {
          setAttendanceDialogInfo({
            isOpen: true,
            worker: {
              id: worker.id,
              name: `${worker.firstName} ${worker.lastName}`,
              mle: worker.mle || "",
            },
            date: new Date(statusMenuInfo.date),
          });
        }
      }
    }
    closeStatusMenu();
  };

  const handleCloseAttendanceSheetDialog = () => {
    setAttendanceDialogInfo({ isOpen: false, worker: null, date: null });
  };

  const handleCloseOperatorDetailsDialog = () => {
    setOperatorDetailsDialogInfo({ isOpen: false, operatorId: "", operatorName: "" });
  };

  // Function to refresh data after manual time interval is added
  const handleDataRefresh = async () => {
    try {
      const endDate = new Date().toISOString().split("T")[0];
      await fetchShiftLeaderStatusMonitoring(endDate);
    } catch (error) {
      console.error("Failed to refresh data:", error);
    }
  };

  const handleClosePreviousDayAttendanceSheetDialog = () => {
    setAttendanceSheetDialogInfo({ isOpen: false, worker: { id: "", name: "" }, date: new Date() });
  };

  const handlePreviousDayMoreDetails = () => {
    if (statusMenuInfo) {
      // For Team Leader role, use status monitoring data
      if (userRole === UserRole.TEAM_LEADER && statusMonitoringData?.status_data) {
        const operator = statusMonitoringData.status_data.find(
          (op) => op.legacy_id.toString() === statusMenuInfo.workerId,
        );
        if (operator) {
          setOperatorDetailsDialogInfo({
            isOpen: true,
            operatorId: operator.id, // Use the UUID from the API
            operatorName: `${operator.first_name} ${operator.last_name}`,
          });
        }
      }
    }
  };

  const handlePreviousDayHistory = () => {
    if (statusMenuInfo) {
      // For Team Leader role, use status monitoring data
      if (userRole === UserRole.TEAM_LEADER && statusMonitoringData?.status_data) {
        const operator = statusMonitoringData.status_data.find(
          (op) => op.legacy_id.toString() === statusMenuInfo.workerId,
        );
        if (operator) {
          setAttendanceSheetDialogInfo({
            isOpen: true,
            worker: {
              id: operator.id,
              name: `${operator.first_name} ${operator.last_name}`,
            },
            date: new Date(statusMenuInfo.date),
          });
        }
      }
    }
  };

  const renderAttendanceCell = (
    workerId: string,
    date: string,
    isToday: boolean,
    readOnly: boolean = false,
  ) => {
    const key = `${workerId}_${date}`;
    const sequence = attendanceSheetDetails[key];
    const CHUNK_SIZE = 4;





    // TEAM_LEADER rendering uses statusMonitoringData
    if (userRole === UserRole.TEAM_LEADER && statusMonitoringData?.status_data) {
      // Find the operator by legacy_id (converted to string as workerId)
      const operator = statusMonitoringData.status_data.find(
        (op) => op.legacy_id.toString() === workerId
      );

      if (operator) {

        // Find the daily record for this specific date
        const dailyRecord = operator.dailyRecords.find(record => record.date === date);



        if (dailyRecord && dailyRecord.statusCodes.length > 0) {

          if (dailyRecord.statusCodes.length === 1) {
            const statusCode = dailyRecord.statusCodes[0];
            const status = statusCode.code as AttendanceStatus;
            const statusBgColor = getStatusColor(status);
            const statusTime = formatTime(statusCode.startDateTime);

            if (readOnly) {
              return (
                <div
                  className={`w-full h-12 flex flex-col items-center justify-center text-xs font-medium ${statusBgColor}`}
                  title={`Status: ${status} at ${statusTime}`}
                >
                  <div className="font-bold">{status}</div>
                  <div className="text-xs opacity-75">{statusTime}</div>
                </div>
              );
            }

            return (
              <div
                className={`w-full h-12 text-xs font-medium border-0 rounded-none ${statusBgColor} ${
                  isToday ? "cursor-pointer" : "cursor-pointer hover:opacity-80"
                }`}
                onClick={(e) => handleCellClick(workerId, date, isToday, e)}
                title={`Status: ${status} at ${statusTime}`}
              >
                <div className="flex flex-col items-center">
                  <div className="font-bold">{status}</div>
                  <div className="text-xs opacity-75">{statusTime}</div>
                </div>
              </div>
            );
          }



          // order st
          const statusCodes = dailyRecord.statusCodes;
          const latestStatus = statusCodes[statusCodes.length - 1];
          const latestStatusBgColor = getStatusColor(latestStatus.code as AttendanceStatus);


          const CHUNK_SIZE = 3;
          const lines: typeof statusCodes[] = [];
          for (let i = 0; i < statusCodes.length; i += CHUNK_SIZE) {
            lines.push(statusCodes.slice(i, i + CHUNK_SIZE));
          }

          return (
            <div
              className={`w-full h-12 flex flex-col items-center justify-center text-xs font-medium border-0 rounded-none ${latestStatusBgColor} ${
                !readOnly ? "cursor-pointer hover:opacity-80" : "cursor-not-allowed opacity-60"
              } overflow-hidden px-1`}
              onClick={(e) => !readOnly && handleCellClick(workerId, date, isToday, e)}
              title={`Status changes: ${statusCodes.map(sc => `${sc.code} at ${formatTime(sc.startDateTime)}`).join(', ')}`}
            >
              {lines.map((line, lineIdx) => (
                <div key={lineIdx} className="flex items-center justify-center">
                  {line.map((statusCode, idx) => (
                    <span key={idx} className="flex items-center">
                      <span
                        className={`text-xs font-bold ${
                          statusCode.code === "P" || statusCode.code === "P_IN_BUS" || statusCode.code === "P_IN_PLANT"
                            ? "text-green-800"
                            : statusCode.code === "CTN" || statusCode.code === "AB"
                              ? "text-red-800"
                              : "text-gray-800"
                        }`}
                      >
                        {statusCode.code}
                      </span>
                      {idx < line.length - 1 && (
                        <span className="text-gray-800 mx-1">,</span>
                      )}
                    </span>
                  ))}
                  {lineIdx < lines.length - 1 && <span className="text-gray-800">,</span>}
                </div>
              ))}
            </div>
          );
        } else {
          if (isToday && (userRole === UserRole.TEAM_LEADER || userRole === UserRole.SHIFT_LEADER)) {
            const shiftStatus = getShiftStatus();

            if (shiftStatus === "VISUAL_CHECK" || shiftStatus === 'REVIEW') {
              return (
                <div
                  className="w-full h-12 flex flex-col items-center justify-center text-xs font-medium bg-white border cursor-pointer hover:bg-gray-50"
                  onClick={(e) => handleCellClick(workerId, date, isToday, e)}
                  title="Visual Check Active - Click to mark attendance"
                >
                  <div className="text-gray-600 font-medium text-center leading-tight">
                    -
                  </div>
                </div>
              );
            }
          }

          // Default empty state
          return (
            <div className="w-full h-12 flex items-center justify-center text-xs font-medium bg-white">
              -
            </div>
          );
        }
      }
    }

    // DEPARTMENT_CLERK rendering uses clerkStatusMonitoringData
    if (userRole === UserRole.DEPARTEMENT_CLERK && clerkStatusMonitoringData?.status_data) {
      // Find the subordinate by legacy_id (converted to string as workerId)
      const subordinate = clerkStatusMonitoringData.status_data.find(
        (sub) => sub.legacy_id.toString() === workerId
      );

      if (subordinate) {
        // Find the daily record for this specific date
        const dailyRecord = subordinate.dailyRecords.find(record => record.date === date);

        if (dailyRecord && dailyRecord.statusCodes.length > 0) {
          if (dailyRecord.statusCodes.length === 1) {
            const statusCode = dailyRecord.statusCodes[0];
            const status = statusCode.code as AttendanceStatus;
            const statusBgColor = getStatusColor(status);
            const statusTime = formatTime(statusCode.startDateTime);

            if (readOnly) {
              return (
                <div
                  className={`w-full h-12 flex flex-col items-center justify-center text-xs font-medium ${statusBgColor}`}
                  title={`Status: ${status} at ${statusTime}`}
                >
                  <div className="font-bold">{status}</div>
                  <div className="text-xs opacity-75">{statusTime}</div>
                </div>
              );
            }

            return (
              <div
                className={`w-full h-12 text-xs font-medium border-0 rounded-none ${statusBgColor} ${
                  isToday ? "cursor-pointer" : "cursor-pointer hover:opacity-80"
                }`}
                onClick={(e) => handleCellClick(workerId, date, isToday, e)}
                title={`Status: ${status} at ${statusTime}`}
              >
                <div className="flex flex-col items-center">
                  <div className="font-bold">{status}</div>
                  <div className="text-xs opacity-75">{statusTime}</div>
                </div>
              </div>
            );
          }

          // Multiple status codes - show them in compact lines with latest status background
          const statusCodes = dailyRecord.statusCodes;
          const latestStatus = statusCodes[statusCodes.length - 1];
          const latestStatusBgColor = getStatusColor(latestStatus.code as AttendanceStatus);

          // Split status codes into lines of 3
          const CHUNK_SIZE = 3;
          const lines: typeof statusCodes[] = [];
          for (let i = 0; i < statusCodes.length; i += CHUNK_SIZE) {
            lines.push(statusCodes.slice(i, i + CHUNK_SIZE));
          }

          return (
            <div
              className={`w-full h-12 flex flex-col items-center justify-center text-xs font-medium border-0 rounded-none ${latestStatusBgColor} ${
                !readOnly ? "cursor-pointer hover:opacity-80" : "cursor-not-allowed opacity-60"
              } overflow-hidden px-1`}
              onClick={(e) => !readOnly && handleCellClick(workerId, date, isToday, e)}
              title={`Status changes: ${statusCodes.map(sc => `${sc.code} at ${formatTime(sc.startDateTime)}`).join(', ')}`}
            >
              {lines.map((line, lineIdx) => (
                <div key={lineIdx} className="flex items-center justify-center">
                  {line.map((statusCode, idx) => (
                    <span key={idx} className="flex items-center">
                      <span
                        className={`text-xs font-bold ${
                          statusCode.code === "P" || statusCode.code === "P_IN_BUS" || statusCode.code === "P_IN_PLANT"
                            ? "text-green-800"
                            : statusCode.code === "CTN" || statusCode.code === "AB"
                              ? "text-red-800"
                              : "text-gray-800"
                        }`}
                      >
                        {statusCode.code}
                      </span>
                      {idx < line.length - 1 && (
                        <span className="text-gray-800 mx-1">,</span>
                      )}
                    </span>
                  ))}
                  {lineIdx < lines.length - 1 && <span className="text-gray-800">,</span>}
                </div>
              ))}
            </div>
          );
        } else {
          if (isToday) {
            return (
              <div
                className="w-full h-12 flex flex-col items-center justify-center text-xs font-medium bg-white border cursor-pointer hover:bg-gray-50"
                onClick={(e) => handleCellClick(workerId, date, isToday, e)}
                title="Click to mark attendance"
              >
                <div className="text-gray-600 font-medium text-center leading-tight">
                  -
                </div>
              </div>
            );
          }

          // Default empty state
          return (
            <div className="w-full h-12 flex items-center justify-center text-xs font-medium bg-white">
              -
            </div>
          );
        }
      }
    }

    // SHIFT_LEADER rendering depends on activeTab and the correct data source
    if (userRole === UserRole.SHIFT_LEADER) {
      // Determine data source based on tab: teamLeaders on my-teamleaders, operators on my-operators
      const dataSource: any[] =
        activeTab === "my-teamleaders"
          ? (teamLeadersStatusData || [])
          : ((operatorsStatusData && operatorsStatusData.length > 0
              ? operatorsStatusData
              : (statusMonitoringData?.status_data || [])) as any[]);

      const operator = dataSource.find(
        (op: any) => op?.legacy_id?.toString() === workerId
      );

      if (operator) {
        // Find the daily record for this specific date
        const dailyRecord = (operator.dailyRecords || []).find((record: any) => record.date === date);

        if (dailyRecord && dailyRecord.statusCodes.length > 0) {
          // If there's only one status code, show it directly
          if (dailyRecord.statusCodes.length === 1) {
            const statusCode = dailyRecord.statusCodes[0];
            const status = statusCode.code as AttendanceStatus;
            const statusBgColor = getStatusColor(status);
            const statusTime = formatTime(statusCode.startDateTime);

            if (readOnly) {
              return (
                <div
                  className={`w-full h-12 flex flex-col items-center justify-center text-xs font-medium ${statusBgColor}`}
                  title={`Status: ${status} at ${statusTime}`}
                >
                  <div className="font-bold">{status}</div>
                  <div className="text-xs opacity-75">{statusTime}</div>
                </div>
              );
            }

            return (
              <Button
                variant="ghost"
                size="sm"
                className={`w-full h-12 text-xs font-medium border-0 rounded-none ${statusBgColor} ${
                  isToday ? "cursor-pointer" : "cursor-pointer hover:opacity-80"
                }`}
                onClick={(e) => handleCellClick(workerId, date, isToday, e)}
                disabled={false}
                title={`Status: ${status} at ${statusTime}`}
              >
                <div className="flex flex-col items-center">
                  <div className="font-bold">{status}</div>
                  <div className="text-xs opacity-75">{statusTime}</div>
                </div>
              </Button>
            );
          }

          // Multiple status codes - show them in compact lines with latest status background
          const statusCodes = dailyRecord.statusCodes;
          const latestStatus = statusCodes[statusCodes.length - 1];
          const latestStatusBgColor = getStatusColor(latestStatus.code as AttendanceStatus);

          // Split status codes into lines of 3
          const CHUNK_SIZE = 3;
          const lines: typeof statusCodes[] = [];
            for (let i = 0; i < statusCodes.length; i += CHUNK_SIZE) {
              lines.push(statusCodes.slice(i, i + CHUNK_SIZE));
            }

           return (
             <div
              className={`w-full h-12 flex flex-col items-center justify-center text-xs font-medium border-0 rounded-none ${latestStatusBgColor} ${
                !readOnly ? "cursor-pointer hover:opacity-80" : "cursor-not-allowed opacity-60"
              } overflow-hidden px-1`}
              onClick={(e) => !readOnly && handleCellClick(workerId, date, isToday, e)}
              title={`Status changes: ${statusCodes.map((sc: any) => `${sc.code} at ${formatTime(sc.startDateTime)}`).join(', ')}`}
            >
              {lines.map((line: any[], lineIdx: number) => (
                <div key={lineIdx} className="flex items-center justify-center">
                  {line.map((statusCode: any, idx: number) => (
                    <span key={idx} className="flex items-center">
                      <span
                        className={`text-xs font-bold ${
                          statusCode.code === "P" || statusCode.code === "P_IN_BUS" || statusCode.code === "P_IN_PLANT"
                            ? "text-green-800"
                            : statusCode.code === "CTN" || statusCode.code === "AB"
                              ? "text-red-800"
                              : "text-gray-800"
                        }`}
                      >
                        {statusCode.code}
                      </span>
                      {idx < line.length - 1 && (
                        <span className="text-gray-800 mx-1">,</span>
                      )}
                    </span>
                  ))}
                  {lineIdx < lines.length - 1 && <span className="text-gray-800">,</span>}
                </div>
              ))}
            </div>
          );
        } else {
          if (isToday) {

              return (
                <div
                  className="w-full h-12 flex flex-col items-center justify-center text-xs font-medium bg-white border cursor-pointer hover:bg-gray-50"
                  onClick={(e) => handleCellClick(workerId, date, isToday, e)}
                  title="Visual Check Active - Click to mark attendance"
                >
                  <div className="text-gray-600 font-medium text-center leading-tight">
                    -
                  </div>
                </div>
              );

          }

          // Default empty state
          return (
            <div className="w-full h-12 flex items-center justify-center text-xs font-medium bg-white">
              -
            </div>
          );
        }
      }
    }

    // For non-today dates, use status history data
    if (!isToday) {
      let statusHistory: AttendanceStatus;

      // For other roles, use existing attendance data
      if (userRole !== UserRole.TEAM_LEADER && userRole !== UserRole.SHIFT_LEADER) {
        statusHistory = getAttendanceForWorkerAndDate(workerId, date);
      } else {
        statusHistory = "";
      }

      return (
        <div
          className={`w-full h-12 flex items-center justify-center text-xs font-medium ${getStatusColor(statusHistory)}`}
        >
          {statusHistory}
        </div>
      );
    }

    if (sequence && sequence.length > 0) {
      // If all values in the sequence are the same and not in readOnly mode, show only that value as a clickable button
      if (!readOnly && sequence.every((v) => v === sequence[0])) {
        const status = sequence[0];
        return (
          <Button
            variant="ghost"
            size="sm"
            className={`w-full h-12 text-xs font-medium border-0 rounded-none ${getStatusColor(status as AttendanceStatus)} ${
              isToday ? "cursor-pointer" : "cursor-not-allowed opacity-60"
            }`}
            onClick={(e) => handleCellClick(workerId, date, isToday, e)}
            disabled={!isToday}
          >
            {status}
          </Button>
        );
      }
      // Otherwise, show the sequence as before
      const lines: string[][] = [];
      for (let i = 0; i < sequence.length; i += CHUNK_SIZE) {
        lines.push(sequence.slice(i, i + CHUNK_SIZE));
      }
      return (
        <div
          className={`flex flex-col items-center justify-center gap-0.5 min-h-[48px] ${readOnly ? "" : "cursor-pointer"}`}
          {...(!readOnly && {
            onClick: (e: React.MouseEvent) => {
              if (!isToday) return;
              const cellRect = (
                e.currentTarget as HTMLElement
              ).getBoundingClientRect();
              const containerRect =
                containerRef.current?.getBoundingClientRect();
              let x = cellRect.left;
              let y = cellRect.bottom;
              if (containerRect && containerRef.current) {
                x =
                  cellRect.left -
                  containerRect.left +
                  containerRef.current.scrollLeft +
                  cellRect.width;
                y =
                  cellRect.bottom -
                  containerRect.top +
                  containerRef.current.scrollTop;
              }
              setStatusMenuInfo({
                isOpen: true,
                position: { x, y },
                workerId,
                date,
                sequenceMode: true,
              });
            },
          })}
        >
          {lines.map((line, lineIdx) => (
            <div
              key={lineIdx}
              className="flex flex-wrap items-center justify-center"
            >
              {line.map((status, idx) => (
                <span
                  key={idx}
                  className={`text-xs font-bold ${
                    status === "P"
                      ? "text-green-600"
                      : status === "CTN" || status === "AB"
                        ? "text-red-600"
                        : "text-gray-600"
                  }`}
                  style={{ marginRight: idx < line.length - 1 ? 2 : 0 }}
                >
                  {status}
                  {idx < line.length - 1 ? (
                    <span className="text-black">, </span>
                  ) : null}
                </span>
              ))}
            </div>
          ))}
        </div>
      );
    }
    // fallback to default button
    let status: AttendanceStatus;

    // For Team Leader role, use status monitoring data
    if (userRole === UserRole.TEAM_LEADER && statusMonitoringData?.status_data) {
      const operator = statusMonitoringData.status_data.find(
        (op) => op.legacy_id.toString() === workerId
      );

      if (operator) {
        // Find the daily record for this specific date
        const dailyRecord = operator.dailyRecords.find(record => record.date === date);

        if (dailyRecord && dailyRecord.statusCodes.length > 0) {
          // Get the latest status code for this date
          const latestStatus = dailyRecord.statusCodes[dailyRecord.statusCodes.length - 1];
          status = latestStatus.code as AttendanceStatus;
        } else {
          status = "";
        }
      } else {
        status = "";
      }
    } else {
      status = getAttendanceForWorkerAndDate(workerId, date);
    }

    if (readOnly) {
      return (
        <div
          className={`w-full h-12 flex items-center justify-center text-xs font-medium ${getStatusColor(status)}`}
        >
          {status}
        </div>
      );
    }
    return (
      <Button
        variant="ghost"
        size="sm"
        className={`w-full h-12 text-xs font-medium border-0 rounded-none ${getStatusColor(status)} ${
          isToday ? "cursor-pointer" : "cursor-not-allowed opacity-60"
        }`}
        onClick={(e) => handleCellClick(workerId, date, isToday, e)}
        disabled={!isToday}
      >
        {status}
      </Button>
    );
  };

  // Handler for clicking a team in clocking validation column
  const handleClockingValidationCellClick = async (
    team: TeamStatus,
    workerId: string,
    teamLeaderInstantId: any,
  ) => {


    console.log('TeamStatus' , team);
    console.log('workerId' , workerId);



    useAttendanceStore.getState().setSelectedTeamLeaderInstantId(teamLeaderInstantId);
    useAttendanceStore.getState().setSelectedValidationTeam(team);
    setSelectedWorkerId(workerId);
    setIsClockingValidationDialogOpen(true);



    let worker = filteredWorkers.find((w) => w.id === workerId);

    console.log('worker_selected' , worker);
    setSelectedTeamLeader({
      id: worker?.id || "",
      name: worker?.firstName + " " + worker?.lastName || "",

      // TODO: add it dynamicly
      status: "Present (Line)",
      statusType: "present",
    })

    
  };


  const handleCloseClockingValidationDialog = () => {
    setIsClockingValidationDialogOpen(false);
    setSelectedWorkerId(null);
  };



  const handleReviewSpecialView = async (comment: string) => {


    if (!selectedValidationTeam) return;

    const selectedTlInstant = useAttendanceStore.getState().selectedTeamLeaderInstantId;
    const payload = {
      shiftLeaderInstantId: statusMonitoringData?.shift_instant_id || "",
      teamLeaderInstantId: selectedTlInstant || "",
      teamId: selectedValidationTeam.teamId,
      comment: comment,
      status: "REVIEW" as const,
    };
    
    try {
      console.log("Submitting team status change payload", payload);
      await changeTeamStatus(payload);
    } catch (e) {
      console.error("Failed to validate team status:", e);
    }
    useAttendanceStore.getState().setSelectedValidationTeam(null);
    setSelectedValidationWorkerId(null);
    setIsSendForReviewDialogOpen(false);
  };

  // Handler for confirm button in special view
  const handleConfirmSpecialView = async () => {
    if (!selectedValidationTeam) return;
    

    const selectedTlInstant = useAttendanceStore.getState().selectedTeamLeaderInstantId;
    const payload = {
      shiftLeaderInstantId: statusMonitoringData?.shift_instant_id || "",
      teamLeaderInstantId: selectedTlInstant || "",
      teamId: selectedValidationTeam.teamId,
      status: "VALIDATED" as const,
    };
    
    try {
      console.log("Submitting team status change payload", payload);
      await changeTeamStatus(payload);
    } catch (e) {
      console.error("Failed to validate team status:", e);
    }
    useAttendanceStore.getState().setSelectedValidationTeam(null);
    setSelectedValidationWorkerId(null);
  };
  // Replace dialog
  const [isReplaceDialogOpen, setIsReplaceDialogOpen] = useState(false);
  const [replaceWorkerMle, setReplaceWorkerMle] = useState<string | null>(null);
  const [absentEmployeeOperator, setAbsentEmployeeOperator] = useState<
    Operator | undefined
  >(undefined);
  const [isLoadingOperator, setIsLoadingOperator] = useState(false);
  const [operatorError, setOperatorError] = useState<string | null>(null);

  const { fetchOperatorById } = useReplacementStore();

  const handleOpenReplaceDialog = () => {
    // Get the worker ID from the status menu context and find the corresponding MLE
    if (statusMenuInfo) {
      const worker = workers.find((w) => w.id === statusMenuInfo.workerId);
      if (worker) {
        setReplaceWorkerMle(worker.mle); // Use MLE instead of ID
      }
    }
    setIsReplaceDialogOpen(true);
    // Reset states when opening dialog
    setAbsentEmployeeOperator(undefined);
    setOperatorError(null);
  };

  // Fetch operator data when replace dialog opens and workerId is available
  useEffect(() => {
    const fetchAbsentEmployee = async () => {
      if (isReplaceDialogOpen && replaceWorkerMle) {
        setIsLoadingOperator(true);
        setAbsentEmployeeOperator(undefined);
        setOperatorError(null);

        try {
          // Use the worker's MLE value for the API call
          const operatorId = replaceWorkerMle; // This is now the MLE value
          const teamLeaderId = "3e2bac24-5866-4065-8a7b-914c2e077cf1";
          const shiftId = "b4bedff2-165e-4156-969f-d3b3cd025970";

          const operatorData = await fetchOperatorById(
            operatorId,
            teamLeaderId,
            shiftId,
          );


          console.log("operatorData " , operatorData)

          if (operatorData) {
            setAbsentEmployeeOperator(operatorData);
          } else {
            setOperatorError("No operator data found for this employee");
          }
        } catch (error) {
          console.error("Failed to fetch operator data:", error);
          setOperatorError("Failed to load operator data. Please try again.");
        } finally {
          setIsLoadingOperator(false);
        }
      }
    };

    fetchAbsentEmployee();
  }, [isReplaceDialogOpen, replaceWorkerMle, fetchOperatorById]);

  // Copy icons from MyTeamHeader
  const ProjectIcons = (
    <CustomIcon
      name="project"
      className="mr-2"
      style={{ width: "17px", height: "18px", fill: "#000000" }}
    />
  );
  const FamilyIcons = (
    <CustomIcon
      name="family"
      className="mr-2"
      style={{ width: "17px", height: "18px", fill: "#9E7A00" }}
    />
  );
  const LineChartIcons = (
    <CustomIcon
      name="lineChart"
      className="mr-2"
      style={{ width: "17px", height: "18px", fill: "#4CAF50" }}
    />
  );
  const AreaIcons = (
    <CustomIcon
      name="areaChart"
      className="mr-2"
      style={{ width: "17px", height: "18px", fill: "#c59800" }}
    />
  );
  const TeamIcons = (
    <CustomIcon
      name="team"
      className="mr-2"
      style={{ width: "17px", height: "18px", fill: "#8804A1" }}
    />
  );
  const CustomerIcons = (
    <CustomIcon
      name="customer"
      className="mr-2"
      style={{ width: "17px", height: "18px", fill: "#ffb72f" }}
    />
  );

  // Render the shift leader team sheet view (header + read-only table)
  if (selectedValidationTeam && !isClockingValidationDialogOpen) {
    // Use fetched teamOperatorsStatusData from API to render read-only table
    const ops = teamOperatorsStatusData?.status_data || [];
    // Collect unique dates from data to build calendar header
    const allDates = new Set<string>();
    ops.forEach((op) => {
      (op.dailyRecords || []).forEach((r) => allDates.add(r.date));
    });
    const calendarDates = Array.from(allDates).sort();
    const inlineCalendarDays = calendarDates.map((dateStr) => {
      const d = new Date(dateStr + "T00:00:00");
      return {
        day: d.getDate(),
        date: dateStr,
        dayName: d.toLocaleDateString("en-US", { weekday: "short" }),
        isToday: d.toDateString() === new Date().toDateString(),
      };
    });
    return (
      <div className="flex flex-col gap-2">
        {/* Header - filters row styled like Team Leader view */}
        <div className="flex items-center gap-2 mb-2">
          <div className="bg-[#F5FBFF] border border-[#87B0E6] rounded-lg p-2 flex-shrink-0 mr-2">
            <button
              onClick={() =>
                useAttendanceStore.getState().setSelectedValidationTeam(null)
              }
              className="p-2 rounded hover:bg-gray-200 flex items-center"
              style={{ minWidth: 40 }}
              aria-label="Return"
            >
              <ChevronLeft className="w-5 h-5" />
            </button>
          </div>
          <div className="bg-[#F5FBFF] border border-[#87B0E6] rounded-lg p-2 flex-grow">
            <div className="flex items-center gap-2 flex-nowrap h-full">
              <div className="flex-1 min-w-[200px]">
                <DatePicker
                  label="Date"
                  placeholder="Select a date"
                  className="pointer-events-none opacity-60"
                />
              </div>
              <div className="flex-1 min-w-[200px]">
                <CustomSelect
                  options={[]}
                  onValueChange={() => {}}
                  placeholder="Select customer"
                  label={
                    <span className="flex items-center">
                      {CustomerIcons} Customer
                    </span>
                  }
                  disabled
                />
              </div>
              <div className="flex-1 min-w-[200px]">
                <CustomSelect
                  options={[]}
                  onValueChange={() => {}}
                  placeholder="Select project"
                  label={
                    <span className="flex items-center">
                      {ProjectIcons} Project
                    </span>
                  }
                  disabled
                />
              </div>
              <div className="flex-1 min-w-[200px]">
                <CustomSelect
                  options={[]}
                  onValueChange={() => {}}
                  placeholder="Select family"
                  label={
                    <span className="flex items-center">
                      {FamilyIcons} Family
                    </span>
                  }
                  disabled
                />
              </div>
              <div className="flex-1 min-w-[200px]">
                <CustomSelect
                  options={[]}
                  onValueChange={() => {}}
                  placeholder="Select value stream"
                  label={
                    <span className="flex items-center">
                      {LineChartIcons} Value Stream
                    </span>
                  }
                  disabled
                />
              </div>
              <div className="flex-1 min-w-[200px]">
                <CustomSelect
                  options={[]}
                  onValueChange={() => {}}
                  placeholder="Select area"
                  label={
                    <span className="flex items-center">{AreaIcons} Area</span>
                  }
                  disabled
                />
              </div>
              <div className="flex-1 min-w-[200px]">
                <CustomSelect
                  options={[]}
                  onValueChange={() => {}}
                  placeholder="Select Team"
                  label={
                    <span className="flex items-center">{TeamIcons} Team</span>
                  }
                  disabled
                />
              </div>
            </div>
          </div>
        </div>
        {/* Attendance sheet info row */}
        <div className="flex items-center gap-3 flex-shrink-0 min-w-0 mt-1">
          <div className="p-1 rounded-md flex-shrink-0">
            <CustomIcon
              name="dayView"
              style={{ width: "30px", height: "30px", fill: "#5B7291" }}
            />
          </div>
          <div className="min-w-0">
            <h1 className="text-lg font-medium text-[#5B7291] truncate">
              Attendance sheet Team Leader 4857 - Amine SIDKI -{" "}
              {selectedValidationTeam.teamName}
            </h1>
            <p className="text-sm text-[#8CA2C0] truncate">
              Morning shift (06:00 AM to 14:00 AM)
            </p>
          </div>
          <div className="flex gap-3 ml-auto">
            <Button
              className="flex items-center gap-2 px-3 py-5 rounded-lg border-2 border-green-600 bg-black text-white font-semibold shadow hover:bg-green-900 focus:outline-none"
              style={{ borderColor: "#1DB954" }}
              onClick={handleConfirmSpecialView}
            >
              <svg
                width="20"
                height="20"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                className="text-green-400"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M5 13l4 4L19 7"
                />
              </svg>
              Confirm
            </Button>
            <Button
              className="flex items-center gap-2 px-3 py-5 rounded-lg border-2 border-red-600 bg-black text-white font-semibold shadow hover:bg-red-900 focus:outline-none"
              style={{ borderColor: "#FF3B30" }}
              onClick={handleOpenSendForReviewDialog}
            >
              <svg
                width="20"
                height="20"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                className="text-red-400"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M15 19l-7-7 7-7"
                />
              </svg>
              Send for review
            </Button>
          </div>
        </div>
        {/* Team Leader Table with checkbox hidden */}
        <div
          className="rounded-lg p-2 flex-grow overflow-x-auto"
          style={{ minHeight: "400px", background: "#fff", border: "none" }}
        >
          <table
            className="w-full border-separate border-spacing-0 table-layout-fixed"
            style={{ borderColor: "#9D9D9D" }}
          >
            <thead className="sticky top-0 bg-white z-10">
              {/* Today indicator row above headers */}
              <th
                className="sticky left-0 z-30 p-1 h-6"
                style={{ backgroundColor: "white" }}
              ></th>
              <th
                className="sticky left-[80px] z-30 p-1 h-6"
                style={{ backgroundColor: "white" }}
              ></th>
              <th
                className="sticky left-[180px] z-30 p-1 h-6"
                style={{ backgroundColor: "white" }}
              ></th>
              <th
                className="sticky left-[280px] z-30 p-1 h-6"
                style={{ backgroundColor: "white" }}
              ></th>
              <th
                className="sticky left-[380px] z-30 p-1 h-6"
                style={{ backgroundColor: "white" }}
              ></th>
              {calendarDays.map(({ day, isToday }) => (
                <th
                  key={`today-${day}`}
                  className="p-1 h-6 min-w-[60px] z-5"
                  style={{ backgroundColor: "white", border: "none" }}
                >
                  {isToday && (
                    <div className="text-xs font-semibold text-[#4762F1] rounded px-1 ">
                      TODAY
                    </div>
                  )}
                </th>
              ))}
            </thead>
            <thead className="border border-b-5 border-[#000000] ">
              <tr>
                <th
                  className="sticky left-0 z-30 border p-2 text-left min-w-[80px] text-sm font-medium"
                  style={{
                    backgroundColor: "#BFD5F1",
                    borderColor: "#9D9D9D",
                    borderBottom: "5px solid #000000",
                  }}
                >
                  Mle
                </th>
                <th
                  className="sticky left-[80px] z-30 border p-2 text-left w-[100px] text-sm font-medium"
                  style={{
                    backgroundColor: "#BFD5F1",
                    borderColor: "#9D9D9D",
                    borderBottom: "5px solid #000000",
                  }}
                >
                  First Name
                </th>
                <th
                  className="sticky left-[180px] z-30 border p-2 text-left w-[100px] text-sm font-medium"
                  style={{
                    backgroundColor: "#BFD5F1",
                    borderColor: "#9D9D9D",
                    borderBottom: "5px solid #000000",
                  }}
                >
                  Last Name
                </th>
                <th
                  className="sticky left-[280px] z-30 border p-2 text-left w-[100px] text-sm font-medium"
                  style={{
                    backgroundColor: "#BFD5F1",
                    borderColor: "#9D9D9D",
                    borderBottom: "5px solid #000000",
                  }}
                >
                  Role
                </th>
                <th
                  className="sticky left-[380px] z-30 border p-2 text-left w-[100px] text-sm font-medium"
                  style={{
                    backgroundColor: "#BFD5F1",
                    borderColor: "#9D9D9D",
                    borderBottom: "5px solid #000000",
                  }}
                >
                  Function
                </th>
                {calendarDays.map(({ day, dayName, isToday }) => (
                  <th
                    key={day}
                    className="border p-1 text-center min-w-[60px] z-5"
                    style={{
                      backgroundColor: isToday ? "#000000" : "#BFD5F1",
                      borderColor: "#9D9D9D",
                      borderBottom: "5px solid #000000",
                      color: isToday ? "#FFFFFF" : "#000000",
                    }}
                  >
                    <div className="text-xs font-medium">
                      {day} {dayName}
                    </div>
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>
              {ops.map((op) => (
                <tr key={op.id}>
                  <td
                    className="sticky left-0 border p-2 text-sm z-20"
                    style={{
                      backgroundColor: "#F5FBFF",
                      borderColor: "#9D9D9D",
                    }}
                  >
                    <span>{op.legacy_id}</span>
                  </td>
                  <td
                    className="sticky left-[80px] border p-2 text-sm z-20"
                    style={{
                      backgroundColor: "#F5FBFF",
                      borderColor: "#9D9D9D",
                    }}
                  >
                    {op.first_name}
                  </td>
                  <td
                    className="sticky left-[180px] border p-2 text-sm z-20"
                    style={{
                      backgroundColor: "#F5FBFF",
                      borderColor: "#9D9D9D",
                    }}
                  >
                    {op.last_name}
                  </td>
                  <td
                    className="sticky left-[280px] border p-2 text-xs z-20"
                    style={{
                      backgroundColor: "#F5FBFF",
                      borderColor: "#9D9D9D",
                    }}
                  >
                    <div className="text-blue-600 font-medium">{op.role}</div>
                  </td>
                  <td
                    className="sticky left-[380px] border p-2 text-sm z-20"
                    style={{
                      backgroundColor: "#F5FBFF",
                      borderColor: "#9D9D9D",
                    }}
                  >
                    <div className="text-blue-500 font-medium">{op.team_id}</div>
                  </td>
                  {inlineCalendarDays.map(({ day, date }) => {
                    const record = (op.dailyRecords || []).find((r) => r.date === date);
                    const last = record && record.statusCodes && record.statusCodes.length > 0
                      ? record.statusCodes[record.statusCodes.length - 1]
                      : null;
                    const status = last ? last.code : "";
                    return (
                      <td key={day} className="border p-0 relative z-10" style={{ borderColor: "#9D9D9D" }}>
                        <div className="w-full h-12 flex items-center justify-center text-xs font-medium">
                          {status}
                        </div>
                      </td>
                    );
                  })}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        <SendForReviewDialog
          isOpen={isSendForReviewDialogOpen}
          onClose={() => {
            setIsSendForReviewDialogOpen(false);
            useAttendanceStore.getState().setSelectedValidationTeam(null);
            setSelectedValidationWorkerId(null);
          }}
          onSubmit={async (comment) => {
            if (!selectedValidationTeam) return;

            console.log('comment' , comment);


            
            handleReviewSpecialView(comment);
          }}
          teamLeaderId="4857"
          teamLeaderName="Amine SIDKI"
          teamName={safeString(selectedValidationTeam?.teamName)}
          shiftTime="Morning shift (06:00 AM to 14:00 AM)"
        />
      </div>
    );
  }

  return (
    <div ref={containerRef} className="flex-1 overflow-auto pb-9 relative">
      {/* Loading indicator for all user types */}
      {isAnyDataLoading() && (
        <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-50">
          <div className="flex flex-col items-center gap-4">
            <Loader2 className="h-12 w-12 animate-spin text-blue-600" />
            <p className="text-gray-600 font-medium">
              {userRole === UserRole.TEAM_LEADER ? "Loading team data..." :
               userRole === UserRole.DEPARTEMENT_CLERK ? "Loading clerk data..." :
               userRole === UserRole.DEPARTEMENT_MANAGER ? "Loading manager data..." :
               userRole === UserRole.QUALITY_SUPERVISOR ? "Loading quality data..." :
               userRole === UserRole.SHIFT_LEADER ? "Loading shift data..." :
               "Loading data..."}
            </p>
          </div>
        </div>
      )}

      <div className="min-w-max">
        <table
          className="w-full border-separate border-spacing-0 table-layout-fixed"
          style={{ borderColor: "#9D9D9D" }}
        >
          <thead className="sticky top-0 bg-white z-10 ">
            {/* Today indicator row */}
            <tr>
              <th
                className="sticky left-0 z-30 p-1 h-6"
                style={{ backgroundColor: "white" }}
              ></th>
              <th
                className="sticky left-[80px] z-30 p-1 h-6"
                style={{ backgroundColor: "white" }}
              ></th>
              <th
                className="sticky left-[180px] z-30 p-1 h-6"
                style={{ backgroundColor: "white" }}
              ></th>
              {!(
                userRole === UserRole.DEPARTEMENT_CLERK ||
                userRole === UserRole.DEPARTEMENT_MANAGER ||
                (userRole === UserRole.SHIFT_LEADER &&
                  activeTab === "my-operators")
              ) && (
                <th
                  className="sticky left-[280px] z-30 p-1 h-6"
                  style={{ backgroundColor: "white" }}
                ></th>
              )}
              <th
                className="sticky z-30 p-1 h-6"
                style={{
                  left:
                    userRole === UserRole.DEPARTEMENT_CLERK ||
                    userRole === UserRole.DEPARTEMENT_MANAGER ||
                    (userRole === UserRole.SHIFT_LEADER &&
                      activeTab === "my-operators")
                      ? 280
                      : 380,
                  backgroundColor: "white",
                }}
              ></th>

              {userRole === UserRole.QUALITY_SUPERVISOR && (
                <>
                  <th
                    className="sticky left-[480px] z-30 p-1 h-6"
                    style={{ backgroundColor: "white" }}
                  ></th>
                  <th
                    className="sticky left-[580px] z-30 p-1 h-6"
                    style={{ backgroundColor: "white" }}
                  ></th>
                </>
              )}
              {calendarDays.map(({ day, isToday }) => (
                <th
                  key={`today-${day}`}
                  className="p-1 h-6 min-w-[60px] z-5"
                  style={{
                    backgroundColor: "white",
                    border: "none",
                  }}
                >
                  {isToday && (
                    
                    <div className="text-xs font-semibold text-[#4762F1] rounded px-1 ">
                      TODAY
                    </div>
                  )}

                </th>
              ))}
            </tr>

            {/* Column headers row */}
            <tr>
              <th
                className="sticky left-0 z-30 border p-2 text-left min-w-[80px] text-sm font-medium"
                style={{
                  backgroundColor: "#BFD5F1",
                  borderColor: "#9D9D9D",
                  borderRight: "1px solid #9D9D9D",
                  borderBottom: "5px solid #000000",
                }}
              >
                <div className="flex items-center gap-2">
                  {((userRole === UserRole.TEAM_LEADER && isVisualCheckActive) ||
                    (userRole === UserRole.SHIFT_LEADER && activeTab === "my-operators")) || (userRole === UserRole.DEPARTEMENT_CLERK) && (
                    <Checkbox
                      checked={
                        filteredWorkers.every((worker) =>
                          selectedWorkers.has(worker.uuid),
                        ) && filteredWorkersLength > 0
                      }
                      onCheckedChange={handleSelectAll}
                      aria-label="Select all workers"
                    />
                  )}
                  <span>Mle</span>
                </div>
              </th>
              <th
                className="sticky left-[80px] z-30 border p-2 text-left w-[100px] text-sm font-medium"
                style={{
                  backgroundColor: "#BFD5F1",
                  borderColor: "#9D9D9D",
                  borderRight: "1px solid #9D9D9D",
                  borderBottom: "5px solid #000000",
                }}
              >
                First Name
              </th>
              <th
                className="sticky left-[180px] z-30 border p-2 text-left w-[100px] text-sm font-medium"
                style={{
                  backgroundColor: "#BFD5F1",
                  borderColor: "#9D9D9D",
                  borderRight: "1px solid #9D9D9D",
                  borderBottom: "5px solid #000000",
                }}
              >
                Last Name
              </th>
              {!(
                userRole === UserRole.DEPARTEMENT_CLERK ||
                userRole === UserRole.DEPARTEMENT_MANAGER ||
                userRole === UserRole.SHIFT_LEADER
              ) && (
                <th
                  className="sticky left-[280px] z-30 border p-2 text-left w-[100px] text-sm font-medium"
                  style={{
                    backgroundColor: "#BFD5F1",
                    borderColor: "#9D9D9D",
                    borderRight: "1px solid #9D9D9D",
                    borderBottom: "5px solid #000000",
                  }}
                >
                  <div className="text-blue-600 font-medium">Role</div>
                </th>
              )}
              <th
                className="sticky z-30 border p-2 text-left w-[100px] text-sm font-medium"
                style={{
                  left:
                    userRole === UserRole.DEPARTEMENT_CLERK ||
                    userRole === UserRole.DEPARTEMENT_MANAGER ||
                    userRole === UserRole.SHIFT_LEADER
                      ? 280
                      : 380,
                  backgroundColor: "#BFD5F1",
                  borderColor: "#9D9D9D",
                  borderRight: "1px solid #9D9D9D",
                  borderBottom: "5px solid #000000",
                }}
              >
                <div className="text-blue-500 font-medium">Function</div>
              </th>
              {userRole === UserRole.SHIFT_LEADER &&
                activeTab === "my-teamleaders" && (

                  <>
                         <th
                    className="sticky left-[380px] z-30 border p-2 text-left min-w-[150px] text-sm font-medium"
                    style={{
                      backgroundColor: "#BFD5F1",
                      borderColor: "#9D9D9D",
                      borderRight: "none",
                      borderBottom: "5px solid #000000",
                      boxShadow: "4px 0 0 0 #9D9D9D",
                    }}
                  >
                    Clocking Validation
                  </th>


                         <th
                    className="sticky left-[380px] z-30 border p-2 text-left min-w-[150px] text-sm font-medium"
                    style={{
                      backgroundColor: "#BFD5F1",
                      borderColor: "#9D9D9D",
                      borderRight: "none",
                      borderBottom: "5px solid #000000",
                      boxShadow: "4px 0 0 0 #9D9D9D",
                    }}
                  >
                    Shift Type
                  </th>
                  </>
           
                )}
              {userRole === UserRole.QUALITY_SUPERVISOR && (
                <>
                  <th
                    className="sticky left-[480px] z-30 border p-2 text-left w-[100px] text-sm font-medium"
                    style={{
                      backgroundColor: "#BFD5F1",
                      borderColor: "#9D9D9D",
                      borderRight: "1px solid #9D9D9D",
                      borderBottom: "5px solid #000000",
                    }}
                  >
                    Team
                  </th>
                  <th
                    className="sticky left-[580px] z-30 border p-2 text-left w-[100px] text-sm font-medium"
                    style={{
                      backgroundColor: "#BFD5F1",
                      borderColor: "#9D9D9D",
                      borderRight: "none",
                      borderBottom: "5px solid #000000",
                      boxShadow: "4px 0 0 0 #9D9D9D",
                    }}
                  >
                    Line
                  </th>
                </>
              )}
              {calendarDays.map(({ day, dayName, isToday, isFutureStatic }) => (
                <th
                  key={day}
                  className="border p-1 text-center min-w-[60px] z-5"
                  style={{
                    backgroundColor: isToday ? "#000000" : isFutureStatic ? "#E8F4FD" : "#BFD5F1",
                    borderColor: "#9D9D9D",
                    borderBottom: "5px solid #000000",
                    color: isToday ? "#FFFFFF" : "#000000",
                    opacity: isFutureStatic ? 0.7 : 1,
                  }}
                >
                  <div className="text-xs font-medium">
                    {day} {dayName}
                    {isFutureStatic && <div className="text-xs text-gray-500"></div>}
                  </div>
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {filteredWorkers.map((worker) => (
              <tr key={worker.id}>
                <td
                  className="sticky left-0 border p-2 text-sm z-20"
                  style={{
                    backgroundColor: "#F5FBFF",
                    borderColor: "#9D9D9D",
                    borderRight: "1px solid #9D9D9D",
                    borderBottom: "1px solid #9D9D9D",
                  }}
                >
                  <div className="flex items-center gap-2">
                    {((userRole === UserRole.TEAM_LEADER && isVisualCheckActive) ||
                      (userRole === UserRole.SHIFT_LEADER && activeTab === "my-operators")) || (userRole === UserRole.DEPARTEMENT_CLERK) && (
                        <Checkbox
                          checked={selectedWorkers.has(worker.uuid)}
                          onCheckedChange={(checked) =>
                            handleSelectWorker(worker.uuid, checked as boolean)
                          }
                          aria-label={`Select ${worker.firstName} ${worker.lastName}`}
                        />
                      )}
                    <span>{worker.id}</span>
                  </div>
                </td>
                <td
                  className="sticky left-[80px] border p-2 text-sm z-20"
                  style={{
                    backgroundColor: "#F5FBFF",
                    borderColor: "#9D9D9D",
                    borderRight: "1px solid #9D9D9D",
                    borderBottom: "1px solid #9D9D9D",
                  }}
                >
                  {worker.firstName}
                </td>
                <td
                  className="sticky left-[180px] border p-2 text-sm z-20"
                  style={{
                    backgroundColor: "#F5FBFF",
                    borderColor: "#9D9D9D",
                    borderRight: "1px solid #9D9D9D",
                    borderBottom: "1px solid #9D9D9D",
                  }}
                >
                  {worker.lastName}
                </td>
                {!(
                  userRole === UserRole.DEPARTEMENT_CLERK ||
                  userRole === UserRole.DEPARTEMENT_MANAGER ||
                  userRole === UserRole.SHIFT_LEADER
                ) && (
                  <td
                    className="sticky left-[280px] border p-2 text-xs z-20"
                    style={{
                      backgroundColor: "#F5FBFF",
                      borderColor: "#9D9D9D",
                      borderRight: "1px solid #9D9D9D",
                      borderBottom: "1px solid #9D9D9D",
                    }}
                  >
                    <div className="text-blue-600 font-medium">
                      {worker.role}
                    </div>
                  </td>
                )}
                <td
                  className="sticky border p-2 text-sm z-20"
                  style={{
                    left:
                      userRole === UserRole.DEPARTEMENT_CLERK ||
                      userRole === UserRole.DEPARTEMENT_MANAGER ||
                      userRole === UserRole.SHIFT_LEADER
                        ? 280
                        : 380,
                    backgroundColor: "#F5FBFF",
                    borderColor: "#9D9D9D",
                    borderRight: "1px solid #9D9D9D",
                    borderBottom: "1px solid #9D9D9D",
                  }}
                >
                  <div className="text-blue-500 font-medium">
                    {worker.function}
                  </div>
                </td>
                {!(
                  userRole === UserRole.SHIFT_LEADER &&
                  activeTab === "my-operators"
                ) &&
                  userRole === UserRole.SHIFT_LEADER && (

                    <>
                    <td
                      className="sticky left-[380px] border p-2 text-xs z-20"
                      style={{
                        backgroundColor: "#F5FBFF",
                        borderColor: "#9D9D9D",
                        borderRight: "none",
                        borderBottom: "1px solid #9D9D9D",
                        boxShadow: "4px 0 0 0 #9D9D9D",
                      }}
                    >
                      <div
                        className={`font-medium ${getClockingValidationColor((worker.clockingValidation as ClockingValidationStatus) || "")}`}
                        onClick={() => {
                          if (
                            worker.clockingValidation ===
                            "Waiting validation..."
                          ) {
                            const team: TeamStatus = {
                              teamId: worker.team || "",
                              teamName: worker.team || "",
                              status:
                                worker.clockingValidation as ClockingValidationStatus,
                              workerCount: 0,
                              validatedCount: 0,
                              waitingCount: 0,
                              absentCount: 0,
                              notFinishedCount: 0,
                              
                            };

                            handleClockingValidationCellClick(team, worker.mle , worker.teamleader_instant_id || "");
                          }
                        }}
                        style={{
                          cursor:
                            worker.clockingValidation ===
                            "Waiting validation..."
                              ? "pointer"
                              : "default",
                        }}
                      >
                        {worker.clockingValidation || "Not finished yet"}
                      </div>
                    </td>


                    <td 
                        className="sticky left-[380px] border p-2 text-xs z-20"
                      style={{
                        backgroundColor: "#F5FBFF",
                        borderColor: "#9D9D9D",
                        borderRight: "none",
                        borderBottom: "1px solid #9D9D9D",
                        boxShadow: "4px 0 0 0 #9D9D9D",
                      }}
                    >
                       {worker.shift_type}
                    </td>

                    </>
                    
                    
                  )}
                {userRole === UserRole.QUALITY_SUPERVISOR && (
                  <>
                    <td
                      className="sticky left-[480px] border p-2 text-xs z-20"
                      style={{
                        backgroundColor: "#F5FBFF",
                        borderColor: "#9D9D9D",
                        borderRight: "1px solid #9D9D9D",
                        borderBottom: "1px solid #9D9D9D",
                      }}
                    >
                      {worker.team}
                    </td>
                    <td
                      className="sticky left-[580px] border p-2 text-xs z-20"
                      style={{
                        backgroundColor: "#F5FBFF",
                        borderColor: "#9D9D9D",
                        borderRight: "none",
                        borderBottom: "1px solid #9D9D9D",
                        boxShadow: "4px 0 0 0 #9D9D9D",
                      }}
                    >
                      {/* {worker.line} */}
                    </td>
                  </>
                )}
                {calendarDays.map(({ day, date, isToday, isFutureStatic }) => (
                  <td
                    key={day}
                    className="border p-0 relative z-10"
                    style={{ borderColor: "#9D9D9D" }}
                  >
                    {renderAttendanceCell(worker.id, date, isToday, false)}
                  </td>
                ))}
              </tr>
            ))}

            {/* No Results Message */}
            {filteredWorkers.length === 0 && (
              <tr>
                <td
                  colSpan={
                    calendarDays.length +
                    (userRole === UserRole.DEPARTEMENT_CLERK ||
                    userRole === UserRole.DEPARTEMENT_MANAGER ||
                    userRole === UserRole.SHIFT_LEADER
                      ? 3
                      : 4) +
                    (userRole === UserRole.SHIFT_LEADER &&
                    activeTab !== "my-operators"
                      ? 1
                      : 0) +
                    (userRole === UserRole.QUALITY_SUPERVISOR ? 2 : 0)
                  }
                  className="border p-8 text-center"
                  style={{ borderColor: "#9D9D9D" }}
                >
                  <div className="flex flex-col items-center gap-4">
                    <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center">
                      <svg
                        className="w-8 h-8 text-gray-400"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
                        />
                      </svg>
                    </div>
                    <div>
                      <h3 className="text-lg font-medium text-gray-900 mb-2">
                        No Results Found
                      </h3>
                      <p className="text-gray-600">
                        {userRole === UserRole.TEAM_LEADER
                          ? "Please select a team from the filters above to view attendance data."
                          : "No workers found matching the current criteria."}
                      </p>
                    </div>
                  </div>
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
      {statusMenuInfo?.isOpen && !statusMenuInfo.isPreviousDay && (
        <div 
          style={{
            position: "absolute",
            left: statusMenuInfo.position.x,
            top: statusMenuInfo.position.y,
            zIndex: 1000,
          }}
        >

          
          <MyTeamStatusMenue
            shiftType={(
              userRole === UserRole.SHIFT_LEADER && activeTab === "my-teamleaders"
                ? (teamLeaderShiftType || "NORMAL")
                :  (selectedShiftType  || "NORMAL")
            ).toUpperCase()}
            currentStatus={statusMenuInfo.currentStatus!}
            position={{ x: 0, y: 0 }}
            onSelect={handleStatusSelect}
            onClose={closeStatusMenu}
            onOpenRequestDialog={handleOpenRequestDialog}
            onOpenAttendanceSheetDialog={handleOpenAttendanceSheetDialog}
            onOpenReplaceDialog={handleOpenReplaceDialog}
            replace={statusMenuInfo?.replace}
          />
        </div>
      )}

      {/* Previous Day Menu */}
      {statusMenuInfo?.isOpen && statusMenuInfo.isPreviousDay && (
        <PreviousDayMenu
          isOpen={statusMenuInfo.isOpen}
          position={statusMenuInfo.position}
          onClose={closeStatusMenu}
          onMoreDetails={handlePreviousDayMoreDetails}
          onHistory={handlePreviousDayHistory}
          currentStatus={statusMenuInfo.currentStatus!}
          operatorName={
            statusMonitoringData?.status_data?.find(
              (op) => op.legacy_id.toString() === statusMenuInfo.workerId
            )?.first_name + " " +
            statusMonitoringData?.status_data?.find(
              (op) => op.legacy_id.toString() === statusMenuInfo.workerId
            )?.last_name || "Unknown"
          }
          date={statusMenuInfo.date}
        />
      )}
      {/* Request Dialog */}
      <RequestDialog
        isOpen={isRequestDialogOpen}
        onClose={handleCloseRequestDialog}
        onNext={handleOpenRequestForm}
        userInfo={{
          name: "Ahmed Malik",
          id: "123953",
          status: "Present (Line)",
        }}
      />
      {/* Request Form Dialog */}
      <RequestFormDialog
        isOpen={isRequestFormDialogOpen}
        onClose={handleCloseRequestFormDialog}
        onBack={handleBackToRequestList}
        requestType={selectedRequestType}
        userInfo={{
          name: "Ahmed Malik",
          id: "123953",
          status: "Present (Line)",
        }}
      />
      {/* <AttendanceSheetDialog
        isOpen={attendanceDialogInfo.isOpen}
        onClose={handleCloseAttendanceSheetDialog}
        worker={attendanceDialogInfo.worker || { id: "", name: "" }}
        date={attendanceDialogInfo.date || new Date()}
        userInfo={{
          name: "Ahmed Malik",
          id: "123953",
          status: "Present (Line)",
        }}
      /> */}
      {attendanceDialogInfo.isOpen && (
        <DetailsDialog
          isOpen={attendanceDialogInfo.isOpen}
          onClose={handleCloseAttendanceSheetDialog}
          worker={attendanceDialogInfo.worker || { id: "", name: "" }}
          date={attendanceDialogInfo.date || new Date()}
        />
      )}
      {/* ClockingValidationDialog */}
      <ClockingValidationDialog
        userInfo={selectedTeamLeader}
        isOpen={isClockingValidationDialogOpen}
        onClose={handleCloseClockingValidationDialog}
        workerId={selectedWorkerId}
      />
      {isLoadingOperator && isReplaceDialogOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-8 flex flex-col items-center gap-4">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
            <p className="text-gray-600">Loading operator data...</p>
          </div>
        </div>
      )}

      {operatorError && isReplaceDialogOpen && !isLoadingOperator && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-8 flex flex-col items-center gap-4 text-center">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
              <CustomIcon
                name="error"
                style={{ width: "24px", height: "24px", fill: "#DC2626" }}
              />
            </div>
            <div>
              <p className="text-red-600 font-semibold mb-2">
                Failed to load operator data
              </p>
              <p className="text-gray-600">{operatorError}</p>
            </div>
            <div className="flex gap-3">
              <Button
                onClick={() => {
                  setIsReplaceDialogOpen(false);
                  setReplaceWorkerMle(null);
                  setAbsentEmployeeOperator(undefined);
                  setOperatorError(null);
                }}
                variant="outline"
              >
                Close
              </Button>
              <Button
                onClick={() => window.location.reload()}
                className="bg-blue-600 text-white hover:bg-blue-700"
              >
                Retry
              </Button>
            </div>
          </div>
        </div>
      )}

      {!isLoadingOperator && !operatorError && (
        <ReplaceDialog
          isOpen={isReplaceDialogOpen}
          onClose={() => {
            setIsReplaceDialogOpen(false);
            setReplaceWorkerMle(null);
            setAbsentEmployeeOperator(undefined);
            setOperatorError(null);
          }}
          absentEmployee={absentEmployeeOperator}
          teamLeaderId="3e2bac24-5866-4065-8a7b-914c2e077cf1"
          shiftId="b4bedff2-165e-4156-969f-d3b3cd025970"
        />
      )}

      <DetailsDialog
        isOpen={operatorDetailsDialogInfo.isOpen}
        onClose={handleCloseOperatorDetailsDialog}
        operatorId={operatorDetailsDialogInfo.operatorId}
        worker={{ id: operatorDetailsDialogInfo.operatorId, name: operatorDetailsDialogInfo.operatorName }}
        shiftInstantId={statusMonitoringData?.shift_instant_id || ""}
        date={new Date()}
        onDataRefresh={handleDataRefresh}
      />

      {/* Attendance Sheet Dialog for Previous Days */}
      <AttendanceSheetDialog
        isOpen={attendanceSheetDialogInfo.isOpen}
        onClose={handleClosePreviousDayAttendanceSheetDialog}
        worker={attendanceSheetDialogInfo.worker}
        date={attendanceSheetDialogInfo.date}
        userInfo={{
          name: attendanceSheetDialogInfo.worker.name,
          id: attendanceSheetDialogInfo.worker.id,
        }}
      />
    </div>
  );
}

